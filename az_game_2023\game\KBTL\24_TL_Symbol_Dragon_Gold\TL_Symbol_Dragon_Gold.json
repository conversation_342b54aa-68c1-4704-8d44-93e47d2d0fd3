{"skeleton": {"hash": "Usy52DgbucuNkQa/49SX+nyZJUQ", "spine": "3.7.94", "width": 158.1, "height": 153.37, "images": "./images/", "audio": "D:/Work/06_2020/TuLinh/dragon_29_6/Dragon"}, "bones": [{"name": "root"}, {"name": "All", "parent": "root", "x": -0.06, "y": -4.48}, {"name": "Dragon", "parent": "All", "scaleX": 1.1, "scaleY": 1.1}, {"name": "3D", "parent": "Dragon", "y": 150, "color": "abe323ff"}, {"name": "3D_T", "parent": "Dragon", "y": 35, "color": "4a00ffff"}, {"name": "3D_B", "parent": "Dragon", "y": -50, "color": "4a00ffff"}, {"name": "Eyebrow", "parent": "Dragon", "length": 10.4, "rotation": 144.57, "x": -7.68, "y": 16.74, "color": "ff00c6ff"}, {"name": "Eyebrow2", "parent": "Eyebrow", "length": 13.4, "rotation": -1.16, "x": 10.4, "color": "ff00c6ff"}, {"name": "Eyebrow3", "parent": "Eyebrow2", "length": 14.53, "rotation": 13.48, "x": 13.4, "color": "ff00c6ff"}, {"name": "Eye", "parent": "Dragon", "x": -16.16, "y": 17.72}, {"name": "Beard2", "parent": "Dragon", "length": 12.61, "rotation": -160.27, "x": -6.71, "y": -2.44, "color": "ff0000ff"}, {"name": "Beard3", "parent": "Beard2", "length": 15.18, "rotation": 15.58, "x": 12.49, "y": 0.04, "color": "ff0000ff"}, {"name": "Beard4", "parent": "Beard3", "length": 16.64, "rotation": -16.31, "x": 15.18, "color": "ff0000ff"}, {"name": "<PERSON>", "parent": "Dragon", "length": 10.54, "rotation": 165.11, "x": -18.77, "y": 0.78, "color": "ff4509ff"}, {"name": "Beard5", "parent": "<PERSON>", "length": 8.67, "rotation": 29.54, "x": 10.54, "color": "ff4509ff"}, {"name": "Beard6", "parent": "Beard5", "length": 8.86, "rotation": 54.01, "x": 8.67, "color": "ff4509ff"}, {"name": "Beard7", "parent": "Beard6", "length": 10.73, "rotation": 31.03, "x": 8.86, "color": "ff4509ff"}, {"name": "Beard8", "parent": "Beard7", "length": 12.55, "rotation": 2.78, "x": 10.73, "color": "ff4509ff"}, {"name": "Beard9", "parent": "Beard8", "length": 12.52, "rotation": -10.1, "x": 12.55, "color": "ff4509ff"}, {"name": "Beard10", "parent": "Beard9", "length": 6.49, "rotation": -43.33, "x": 12.52, "color": "ff4509ff"}, {"name": "Beard11", "parent": "Beard10", "length": 11.68, "rotation": -65.06, "x": 6.49, "color": "ff4509ff"}, {"name": "Beard12", "parent": "Beard11", "length": 13.14, "rotation": -28.17, "x": 11.68, "color": "ff4509ff"}, {"name": "Beard13", "parent": "Beard12", "length": 11.35, "rotation": -14.27, "x": 13.22, "y": -0.27, "color": "ff4509ff"}, {"name": "Beard14", "parent": "Beard13", "length": 10.53, "rotation": 4.51, "x": 11.35, "color": "ff4509ff"}, {"name": "Beard15", "parent": "Dragon", "x": -19.48, "y": 5.94, "color": "2bff00ff"}, {"name": "Beard17", "parent": "Beard15", "length": 16.67, "rotation": 137.68, "x": 1.17, "y": 17.03, "color": "2bff00ff"}, {"name": "Beard18", "parent": "Beard17", "length": 15.03, "rotation": -26.14, "x": 16.67, "color": "2bff00ff"}, {"name": "Beard19", "parent": "Beard18", "length": 16.72, "rotation": -45.57, "x": 15.03, "color": "2bff00ff"}, {"name": "Beard20", "parent": "Beard15", "length": 14.71, "rotation": 148.3, "x": -1.22, "y": 7.83, "color": "2bff00ff"}, {"name": "Beard21", "parent": "Beard20", "length": 17.17, "rotation": -13.3, "x": 14.71, "color": "2bff00ff"}, {"name": "Beard22", "parent": "Beard21", "length": 12.38, "rotation": -33.86, "x": 17.17, "color": "2bff00ff"}, {"name": "Beard23", "parent": "Beard22", "length": 12.51, "rotation": -11.14, "x": 12.05, "y": -0.69, "color": "2bff00ff"}, {"name": "Beard24", "parent": "Beard15", "length": 15.24, "rotation": 142.85, "x": -1.04, "y": -7.44, "color": "2bff00ff"}, {"name": "Beard25", "parent": "Beard24", "length": 14.39, "rotation": 4.68, "x": 15.24, "color": "2bff00ff"}, {"name": "Beard26", "parent": "Beard25", "length": 13.99, "rotation": -18.4, "x": 14.24, "y": -0.1, "color": "2bff00ff"}, {"name": "Beard27", "parent": "Beard26", "length": 14.67, "rotation": -19.33, "x": 13.99, "color": "2bff00ff"}, {"name": "Beard28", "parent": "Beard15", "length": 11.6, "rotation": 178.18, "x": -0.48, "y": -5.97, "color": "2bff00ff"}, {"name": "Beard29", "parent": "Beard28", "length": 16.71, "rotation": -23.62, "x": 11.6, "color": "2bff00ff"}, {"name": "Beard30", "parent": "Beard29", "length": 17.86, "rotation": -38.26, "x": 16.55, "y": 0.33, "color": "2bff00ff"}, {"name": "Beard31", "parent": "Beard15", "length": 13.91, "rotation": -154.11, "x": 1.36, "y": -5.6, "color": "2bff00ff"}, {"name": "Beard32", "parent": "Beard31", "length": 15.71, "rotation": -31.26, "x": 13.91, "color": "2bff00ff"}, {"name": "Beard33", "parent": "Beard32", "length": 11.98, "rotation": -37.13, "x": 15.71, "color": "2bff00ff"}, {"name": "Beard34", "parent": "Beard33", "length": 13.31, "rotation": -6.97, "x": 11.98, "color": "2bff00ff"}, {"name": "Beard35", "parent": "Beard15", "length": 12.19, "rotation": 151.11, "x": 0.62, "y": -22.9, "color": "2bff00ff"}, {"name": "Beard36", "parent": "Beard35", "length": 15.43, "rotation": 37.11, "x": 12.19, "color": "2bff00ff"}, {"name": "Beard37", "parent": "Beard36", "length": 16.47, "rotation": -26.46, "x": 15.43, "color": "2bff00ff"}, {"name": "Rubi", "parent": "Dragon", "x": -0.09, "y": -25.79}, {"name": "Beard16", "parent": "Dragon", "length": 16.33, "rotation": 142.52, "x": -4.41, "y": 28.64, "color": "0159ffff"}, {"name": "Beard38", "parent": "Beard16", "length": 14.72, "rotation": -30.1, "x": 16.33, "color": "0159ffff"}, {"name": "Beard39", "parent": "Beard38", "length": 15.17, "rotation": -42.41, "x": 14.72, "color": "0159ffff"}, {"name": "Beard40", "parent": "Dragon", "length": 12.12, "rotation": 101.31, "x": -3.98, "y": 30.8, "color": "0159ffff"}, {"name": "Beard41", "parent": "Beard40", "length": 13.8, "rotation": 5.99, "x": 12.12, "color": "0159ffff"}, {"name": "Beard42", "parent": "Beard41", "length": 14.81, "rotation": -40.5, "x": 14.01, "y": -0.06, "color": "0159ffff"}, {"name": "Tong_MouthL", "parent": "Dragon", "x": -13.42, "y": -30.76}, {"name": "Bread_Mouth5", "parent": "Tong_MouthL", "length": 15.43, "rotation": -110.71, "x": -2.45, "y": 18.55, "color": "ff0000ff"}, {"name": "Bread_Mouth6", "parent": "Bread_Mouth5", "length": 8.84, "rotation": -34.02, "x": 15.43, "color": "ff0000ff"}, {"name": "Bread_Mouth7", "parent": "Bread_Mouth6", "length": 11.84, "rotation": -30.16, "x": 8.84, "color": "ff0000ff"}, {"name": "Bread_Mouth4", "parent": "Tong_MouthL", "length": 13.53, "rotation": -122.24, "x": -4.07, "y": 2.29, "color": "ff0000ff"}, {"name": "Bread_Mouth", "parent": "Tong_MouthL", "length": 11.49, "rotation": -101.48, "x": -2.45, "y": -0.46, "color": "ff0000ff"}, {"name": "Bread_Mouth3", "parent": "Dragon", "length": 9.53, "rotation": -85.76, "x": -12, "y": -44.42, "color": "ff0000ff"}, {"name": "Tong_MouthR", "parent": "Dragon", "x": 11.85, "y": -30.76}, {"name": "Bread_Mouth10", "parent": "Dragon", "length": 12.67, "rotation": 95.09, "x": 2.65, "y": -49, "scaleX": -1, "color": "ff0000ff"}, {"name": "Bread_Mouth11", "parent": "Dragon", "x": 1.02, "y": 1.51}, {"name": "<PERSON>", "parent": "Bread_Mouth11", "rotation": -89.2, "x": -9.51, "y": -8.98}, {"name": "Beard43", "parent": "Dragon", "length": 12.12, "rotation": 77.95, "x": 4.89, "y": 30.58, "color": "0159ffff"}, {"name": "Beard44", "parent": "Beard43", "length": 13.8, "rotation": -9.23, "x": 12.12, "color": "0159ffff"}, {"name": "Beard45", "parent": "Beard44", "length": 14.81, "rotation": 45.1, "x": 14.01, "y": -0.06, "color": "0159ffff"}, {"name": "Beard46", "parent": "Dragon", "length": 16.33, "rotation": 44.33, "x": 6.25, "y": 27.99, "color": "0159ffff"}, {"name": "Beard47", "parent": "Beard46", "length": 14.72, "rotation": 24.79, "x": 16.33, "color": "0159ffff"}, {"name": "Beard48", "parent": "Beard47", "length": 15.17, "rotation": 46.24, "x": 14.72, "color": "0159ffff"}, {"name": "Beard49", "parent": "Dragon", "rotation": 179.05, "x": 19.27, "y": 5.94, "color": "2bff00ff"}, {"name": "Beard50", "parent": "Beard49", "length": 16.67, "rotation": -157.73, "x": 1.81, "y": 23.79, "color": "2bff00ff"}, {"name": "Beard51", "parent": "Beard50", "length": 15.03, "rotation": -30.45, "x": 16.67, "color": "2bff00ff"}, {"name": "Beard52", "parent": "Beard51", "length": 16.72, "rotation": 35.45, "x": 15.03, "color": "2bff00ff"}, {"name": "Beard53", "parent": "Beard49", "length": 14.71, "rotation": 166.46, "x": -1.22, "y": 7.83, "color": "2bff00ff"}, {"name": "Beard54", "parent": "Beard53", "length": 17.17, "rotation": 24, "x": 14.71, "color": "2bff00ff"}, {"name": "Beard55", "parent": "Beard54", "length": 12.38, "rotation": 52.02, "x": 17.17, "color": "2bff00ff"}, {"name": "Beard56", "parent": "Beard55", "length": 12.51, "rotation": -16.75, "x": 12.05, "y": -0.69, "color": "2bff00ff"}, {"name": "Beard57", "parent": "Beard49", "length": 15.24, "rotation": -156.72, "x": -0.96, "y": 2.81, "color": "2bff00ff"}, {"name": "Beard58", "parent": "Beard57", "length": 14.39, "rotation": 7.03, "x": 15.24, "color": "2bff00ff"}, {"name": "Beard59", "parent": "Beard58", "length": 13.99, "rotation": 32.49, "x": 14.24, "y": -0.1, "color": "2bff00ff"}, {"name": "Beard60", "parent": "Beard59", "length": 14.67, "rotation": 5.83, "x": 13.99, "color": "2bff00ff"}, {"name": "Beard61", "parent": "Beard49", "length": 11.6, "rotation": 175.52, "x": 0.11, "y": 3.54, "color": "2bff00ff"}, {"name": "Beard62", "parent": "Beard61", "length": 16.71, "rotation": 21.28, "x": 11.6, "color": "2bff00ff"}, {"name": "Beard63", "parent": "Beard62", "length": 17.86, "rotation": 54.57, "x": 16.55, "y": 0.33, "color": "2bff00ff"}, {"name": "Beard64", "parent": "Beard49", "length": 13.91, "rotation": -143.44, "x": 1.36, "y": -5.6, "color": "2bff00ff"}, {"name": "Beard65", "parent": "Beard64", "length": 15.71, "rotation": 1.71, "x": 13.91, "color": "2bff00ff"}, {"name": "Beard66", "parent": "Beard65", "length": 11.98, "rotation": 26.8, "x": 15.71, "color": "2bff00ff"}, {"name": "Beard67", "parent": "Beard66", "length": 13.31, "rotation": 30.98, "x": 11.98, "color": "2bff00ff"}, {"name": "Beard68", "parent": "Beard49", "length": 12.19, "rotation": -150.58, "x": 0.62, "y": -22.9, "color": "2bff00ff"}, {"name": "Beard69", "parent": "Beard68", "length": 15.43, "rotation": 37.11, "x": 12.19, "color": "2bff00ff"}, {"name": "Beard70", "parent": "Beard69", "length": 16.47, "rotation": 54.57, "x": 15.43, "color": "2bff00ff"}, {"name": "Eye2", "parent": "Dragon", "x": 19.17, "y": 17.72}, {"name": "Eyebrow4", "parent": "Dragon", "length": 10.4, "rotation": 37.48, "x": 8.32, "y": 17, "color": "ff00c6ff"}, {"name": "Eyebrow5", "parent": "Eyebrow4", "length": 13.4, "rotation": -12.42, "x": 10.4, "color": "ff00c6ff"}, {"name": "Eyebrow6", "parent": "Eyebrow5", "length": 14.53, "rotation": -8.71, "x": 13.4, "color": "ff00c6ff"}, {"name": "Fang2", "parent": "Bread_Mouth11", "rotation": -89.2, "x": 7.66, "y": -8.98}, {"name": "Beard71", "parent": "Dragon", "length": 12.61, "rotation": -15.94, "x": 4.7, "y": -3.43, "color": "ff0000ff"}, {"name": "Beard72", "parent": "Beard71", "length": 15.18, "rotation": -12.73, "x": 13.06, "y": 0.11, "color": "ff0000ff"}, {"name": "Beard73", "parent": "Beard72", "length": 16.64, "rotation": 12.25, "x": 16.32, "y": -0.07, "color": "ff0000ff"}, {"name": "Bread_Mouth8", "parent": "Tong_MouthR", "length": 15.43, "rotation": -71.8, "x": 3.01, "y": 18.55, "color": "ff0000ff"}, {"name": "Bread_Mouth9", "parent": "Bread_Mouth8", "length": 8.84, "rotation": 32.15, "x": 15.43, "color": "ff0000ff"}, {"name": "Bread_Mouth12", "parent": "Bread_Mouth9", "length": 11.84, "rotation": 37.63, "x": 8.84, "color": "ff0000ff"}, {"name": "Bread_Mouth13", "parent": "Tong_MouthR", "length": 13.53, "rotation": 117.77, "x": 2.38, "y": 2.68, "scaleX": -1, "color": "ff0000ff"}, {"name": "Bread_Mouth2", "parent": "Tong_MouthR", "length": 11.49, "rotation": 105.52, "x": 1.24, "y": -0.46, "scaleX": -1, "color": "ff0000ff"}, {"name": "Bread_Mouth14", "parent": "Dragon", "length": 9.53, "rotation": 93.93, "x": 10.06, "y": -44.42, "scaleX": -1, "color": "ff0000ff"}, {"name": "Bread_Mouth15", "parent": "Dragon", "length": 12.67, "rotation": -89.2, "x": -2.67, "y": -49, "color": "ff0000ff"}, {"name": "Mouth", "parent": "Dragon", "x": -20.1, "y": -10.78, "color": "1a00ffff"}, {"name": "Mouth2", "parent": "Dragon", "x": 17.42, "y": -10.14, "color": "1a00ffff"}, {"name": "Mouth_B", "parent": "Dragon", "y": -55, "color": "00ff0aff"}, {"name": "Beard74", "parent": "Dragon", "length": 10.54, "rotation": 14.2, "x": 15.13, "y": 0.78, "color": "ff4509ff"}, {"name": "Beard75", "parent": "Beard74", "length": 8.67, "rotation": -23.55, "x": 10.54, "color": "ff4509ff"}, {"name": "Beard76", "parent": "Beard75", "length": 8.86, "rotation": -55.78, "x": 8.67, "color": "ff4509ff"}, {"name": "Beard77", "parent": "Beard76", "length": 10.73, "rotation": -36.39, "x": 8.86, "color": "ff4509ff"}, {"name": "Beard78", "parent": "Beard77", "length": 12.55, "rotation": -0.89, "x": 10.73, "color": "ff4509ff"}, {"name": "Beard79", "parent": "Beard78", "length": 12.52, "rotation": 7.22, "x": 12.55, "color": "ff4509ff"}, {"name": "Beard80", "parent": "Beard79", "length": 6.49, "rotation": 38.43, "x": 12.52, "color": "ff4509ff"}, {"name": "Beard81", "parent": "Beard80", "length": 11.68, "rotation": 63.68, "x": 6.49, "color": "ff4509ff"}, {"name": "Beard82", "parent": "Beard81", "length": 13.14, "rotation": 40.76, "x": 11.68, "color": "ff4509ff"}, {"name": "Beard83", "parent": "Beard82", "length": 11.35, "rotation": 9.64, "x": 13.22, "y": -0.27, "color": "ff4509ff"}, {"name": "Beard84", "parent": "Beard83", "length": 10.53, "rotation": 0.08, "x": 11.35, "color": "ff4509ff"}, {"name": "Fx_Rubi", "parent": "Rubi", "x": 0.09, "y": 0.29}, {"name": "Fx_BG", "parent": "All", "scaleX": 1.2, "scaleY": 1.2}, {"name": "Fx_BG2", "parent": "All", "rotation": 98.25, "scaleX": 1.2, "scaleY": 1.2}, {"name": "Fx_BG3", "parent": "All", "rotation": -82.7, "scaleX": 1.2, "scaleY": 1.2}, {"name": "Fx_Rubi2", "parent": "Eye", "x": -0.16, "y": 0.3}, {"name": "Fx_Rubi3", "parent": "Eye2", "x": 0.55, "y": -0.25, "scaleX": -1}, {"name": "Fx_Rubi4", "parent": "Rubi", "rotation": 90.66, "x": 0.09, "y": 0.29}, {"name": "Sparkle", "parent": "Dragon", "x": -22.78, "y": 4.97}, {"name": "Sparkle2", "parent": "Dragon", "rotation": -27.68, "x": 5.19, "y": 28.04, "scaleX": 1.4, "scaleY": 1.4}, {"name": "Sparkle3", "parent": "Dragon", "rotation": 25, "x": 25.13, "y": 3.82, "scaleX": 1.3, "scaleY": 1.3}, {"name": "Sparkle4", "parent": "Dragon", "rotation": 4.23, "x": -2.35, "y": -10.77, "scaleX": 1.5, "scaleY": 1.5}, {"name": "Sparkle5", "parent": "Dragon", "rotation": 13.16, "x": -44.94, "y": 0.55, "scaleX": 1.087, "scaleY": 1.087}, {"name": "Sparkle6", "parent": "Dragon", "rotation": 1.89, "x": -35.23, "y": 49.6, "scaleX": 1.004, "scaleY": 1.004}, {"name": "Sparkle7", "parent": "Dragon", "rotation": -22.76, "x": 6.92, "y": -51.34, "scaleX": 1.106, "scaleY": 1.106}, {"name": "Sparkle8", "parent": "Dragon", "rotation": -11.79, "x": 56.86, "y": 20.35, "scaleX": 0.95, "scaleY": 0.95}, {"name": "khung", "parent": "All", "scaleX": 0.9, "scaleY": 0.9}], "slots": [{"name": "BG", "bone": "khung", "attachment": "BG"}, {"name": "BG2", "bone": "khung", "attachment": "BG"}, {"name": "bg_effect_1", "bone": "khung", "color": "ffffff00", "attachment": "bg_effect_1"}, {"name": "bg_effect_2", "bone": "khung", "color": "ffffff00", "attachment": "bg_effect_1"}, {"name": "<PERSON>ame", "bone": "khung", "attachment": "<PERSON>ame"}, {"name": "Frame2", "bone": "khung", "attachment": "<PERSON>ame"}, {"name": "Frame_Bottom", "bone": "khung", "attachment": "Frame_Bottom"}, {"name": "Frame_Bottom2", "bone": "khung", "attachment": "Frame_Bottom"}, {"name": "Frame_Top2", "bone": "khung", "attachment": "Frame_Top"}, {"name": "Frame_Top", "bone": "khung", "attachment": "Frame_Top"}, {"name": "set3/El00", "bone": "Fx_BG"}, {"name": "set3/El0", "bone": "Fx_BG2"}, {"name": "set3/El1", "bone": "Fx_BG3"}, {"name": "Beard10", "bone": "Beard40", "attachment": "Beard10"}, {"name": "Beard11", "bone": "Beard43", "attachment": "Beard10"}, {"name": "Beard9", "bone": "Beard16", "attachment": "Beard9"}, {"name": "Beard12", "bone": "Beard46", "attachment": "Beard9"}, {"name": "Beard8", "bone": "Beard17", "attachment": "Beard8"}, {"name": "Beard13", "bone": "Beard68", "attachment": "Beard8"}, {"name": "Beard7", "bone": "Beard20", "attachment": "Beard7"}, {"name": "Beard21", "bone": "Beard20"}, {"name": "Beard14", "bone": "Beard64", "attachment": "Beard7"}, {"name": "Beard6", "bone": "Beard24", "attachment": "Beard6"}, {"name": "Beard22", "bone": "Beard24"}, {"name": "Beard15", "bone": "Beard57", "attachment": "Beard6"}, {"name": "Beard5", "bone": "Beard28", "attachment": "Beard5"}, {"name": "Beard16", "bone": "Beard61", "attachment": "Beard5"}, {"name": "Beard4", "bone": "Beard31", "attachment": "Beard4"}, {"name": "Beard17", "bone": "Beard53", "attachment": "Beard4"}, {"name": "Beard3", "bone": "Beard35", "attachment": "Beard3"}, {"name": "Beard18", "bone": "Beard50", "attachment": "Beard3"}, {"name": "Bread_Mouth5", "bone": "Bread_Mouth5", "attachment": "Bread_Mouth5"}, {"name": "Bread_Mouth6", "bone": "Bread_Mouth8", "attachment": "Bread_Mouth5"}, {"name": "Bread_Mouth4", "bone": "Bread_Mouth4", "attachment": "Bread_Mouth4"}, {"name": "Bread_Mouth7", "bone": "Bread_Mouth13", "attachment": "Bread_Mouth4"}, {"name": "Bread_Mouth3", "bone": "Bread_Mouth10", "attachment": "Bread_Mouth3"}, {"name": "Bread_Mouth10", "bone": "Bread_Mouth15", "attachment": "Bread_Mouth3"}, {"name": "Bread_Mouth2", "bone": "Bread_Mouth3", "attachment": "Bread_Mouth2"}, {"name": "Bread_Mouth9", "bone": "Bread_Mouth14", "attachment": "Bread_Mouth2"}, {"name": "Bread_Mouth", "bone": "Bread_Mouth", "attachment": "Bread_Mouth"}, {"name": "Bread_Mouth8", "bone": "Bread_Mouth2", "attachment": "Bread_Mouth"}, {"name": "Mouth", "bone": "Mouth", "attachment": "Mouth"}, {"name": "Mouth2", "bone": "Mouth2", "attachment": "Mouth"}, {"name": "Rubi", "bone": "Rubi", "attachment": "Rubi"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Fang2", "bone": "Fang2", "attachment": "<PERSON>"}, {"name": "Head", "bone": "Dragon", "attachment": "Head"}, {"name": "Head2", "bone": "Dragon", "attachment": "Head"}, {"name": "Eye", "bone": "Eye", "attachment": "Eye"}, {"name": "Eye2", "bone": "Eye2", "attachment": "Eye"}, {"name": "Eyebrow", "bone": "Eyebrow", "attachment": "Eyebrow"}, {"name": "Eyebrow2", "bone": "Eyebrow4", "attachment": "Eyebrow"}, {"name": "Beard2", "bone": "Beard2", "attachment": "Beard2"}, {"name": "Beard19", "bone": "Beard71", "attachment": "Beard2"}, {"name": "<PERSON>", "bone": "<PERSON>", "attachment": "<PERSON>"}, {"name": "Beard20", "bone": "Beard70", "attachment": "<PERSON>"}, {"name": "Nose", "bone": "Dragon", "attachment": "Nose"}, {"name": "Nose2", "bone": "Dragon", "attachment": "Nose"}, {"name": "frame_set/E0", "bone": "Fx_Rubi"}, {"name": "frame_set/E3", "bone": "Fx_Rubi4"}, {"name": "frame_set/E1", "bone": "Fx_Rubi2"}, {"name": "frame_set/E2", "bone": "Fx_Rubi3"}, {"name": "Sparkle", "bone": "Sparkle"}, {"name": "Sparkle2", "bone": "Sparkle2"}, {"name": "Sparkle3", "bone": "Sparkle3"}, {"name": "Sparkle8", "bone": "Sparkle8"}, {"name": "Sparkle4", "bone": "Sparkle4"}, {"name": "Sparkle7", "bone": "Sparkle7"}, {"name": "Sparkle5", "bone": "Sparkle5"}, {"name": "Sparkle6", "bone": "Sparkle6"}, {"name": "demo", "bone": "root", "color": "ffffff8f"}], "transform": [{"name": "3D_B", "order": 0, "bones": ["3D_B"], "target": "3D", "y": -200, "rotateMix": 0, "translateMix": -0.25, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Br_T", "order": 7, "bones": ["Beard16"], "target": "3D_T", "rotation": 142.52, "x": -4.41, "y": -6.36, "shearY": -360, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Br_T2", "order": 8, "bones": ["Beard40"], "target": "3D_T", "rotation": 101.31, "x": -3.98, "y": -4.2, "shearY": -360, "rotateMix": 0, "translateMix": -0.04, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Br_T3", "order": 15, "bones": ["Beard43"], "target": "3D_T", "rotation": 77.95, "x": 4.89, "y": -4.42, "rotateMix": 0, "translateMix": -0.04, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Br_T4", "order": 16, "bones": ["Beard46"], "target": "3D_T", "rotation": 44.33, "x": 6.25, "y": -7.01, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_EyeBrowL", "order": 2, "bones": ["Eyebrow"], "target": "3D_T", "rotation": 144.57, "x": -7.68, "y": -18.26, "shearY": -360, "rotateMix": 0, "translateMix": 0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_EyeBrowR", "order": 3, "bones": ["Eyebrow4"], "target": "3D_T", "rotation": 37.48, "x": 8.32, "y": -18, "rotateMix": 0, "translateMix": 0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_EyeL", "order": 5, "bones": ["Eye"], "target": "3D_T", "x": -16.16, "y": -17.28, "rotateMix": 0, "translateMix": 0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_EyeR", "order": 4, "bones": ["Eye2"], "target": "3D_T", "x": 19.17, "y": -17.28, "rotateMix": 0, "translateMix": 0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_MouthL", "order": 20, "bones": ["Tong_MouthL"], "target": "3D_B", "x": -13.42, "y": 19.24, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_MouthR", "order": 21, "bones": ["Tong_MouthR"], "target": "3D_B", "x": 11.85, "y": 19.24, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Nose", "order": 6, "bones": ["Bread_Mouth11"], "target": "3D_T", "x": 1.02, "y": -33.49, "rotateMix": 0, "translateMix": 0.08, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Rau1", "order": 17, "bones": ["Beard71"], "target": "3D_T", "rotation": -15.94, "x": 4.7, "y": -38.43, "rotateMix": 0, "translateMix": 0.13, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Rau2", "order": 18, "bones": ["Beard2"], "target": "3D_T", "rotation": -160.27, "x": -6.71, "y": -37.44, "rotateMix": 0, "translateMix": 0.13, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauB1", "order": 11, "bones": ["Bread_Mouth3"], "target": "3D_B", "rotation": -85.76, "x": -12, "y": 5.58, "rotateMix": 0, "translateMix": -0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauB2", "order": 12, "bones": ["Bread_Mouth15"], "target": "3D_B", "rotation": -89.2, "x": -2.67, "y": 1, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauB3", "order": 13, "bones": ["Bread_Mouth10"], "target": "3D_B", "rotation": -84.91, "x": 2.65, "y": 1, "scaleX": -2, "shearY": -180, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauB4", "order": 14, "bones": ["Bread_Mouth14"], "target": "3D_B", "rotation": -86.07, "x": 10.06, "y": 5.58, "scaleX": -2, "shearY": -180, "rotateMix": 0, "translateMix": -0.12, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauL2", "order": 22, "bones": ["Beard15"], "target": "3D_T", "x": -19.48, "y": -29.06, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RauR2", "order": 23, "bones": ["Beard49"], "target": "3D_T", "rotation": 179.05, "x": 19.27, "y": -29.06, "shearY": -360, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RiaL", "order": 10, "bones": ["<PERSON>"], "target": "3D_B", "rotation": 165.11, "x": -18.77, "y": 50.78, "shearY": -360, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_RiaR", "order": 9, "bones": ["Beard74"], "target": "3D_B", "rotation": 14.2, "x": 15.13, "y": 50.78, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Rubi", "order": 19, "bones": ["Rubi"], "target": "3D_B", "x": -0.09, "y": 24.21, "rotateMix": 0, "translateMix": -0.08, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T", "order": 1, "bones": ["3D_T"], "target": "3D", "y": -115, "rotateMix": 0, "translateMix": 0.25, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"BG": {"BG": {"x": -31.67, "y": -0.78, "scaleX": 1.2, "scaleY": 1.2, "width": 56, "height": 100}}, "BG2": {"BG": {"x": 31.67, "y": -0.78, "scaleX": -1.2, "scaleY": 1.2, "width": 56, "height": 100}}, "Beard": {"Beard": {"type": "mesh", "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "triangles": [16, 17, 20, 17, 19, 20, 17, 18, 19, 15, 16, 22, 16, 21, 22, 16, 20, 21, 14, 23, 13, 13, 23, 24, 23, 15, 22, 23, 14, 15, 11, 12, 26, 12, 25, 26, 13, 24, 12, 12, 24, 25, 26, 27, 11, 11, 27, 10, 10, 27, 9, 9, 27, 28, 28, 8, 9, 28, 29, 8, 29, 30, 8, 30, 7, 8, 30, 31, 7, 31, 6, 7, 31, 5, 6, 31, 32, 5, 4, 5, 33, 5, 32, 33, 33, 34, 4, 4, 34, 3, 34, 35, 3, 3, 35, 36, 2, 0, 1, 2, 36, 0, 2, 3, 36], "vertices": [1, 13, -1.44, -2.77, 1, 1, 13, -3.01, 3.14, 1, 1, 13, 5.2, 2.02, 1, 3, 13, 11.38, 3.38, 0.09514, 14, 2.4, 2.53, 0.904, 15, -1.64, 6.56, 0.00086, 2, 14, 8.02, 3.87, 0.25718, 15, 2.75, 2.8, 0.74282, 2, 15, 8.42, 3.41, 0.40932, 16, 1.38, 3.15, 0.59068, 2, 16, 7.93, 3.13, 0.957, 17, -2.64, 3.26, 0.043, 1, 17, 5.69, 3.16, 1, 2, 17, 14.33, 3.48, 0.18925, 18, 1.14, 3.74, 0.81075, 2, 18, 10.34, 3.9, 0.97528, 19, -4.27, 1.34, 0.02472, 2, 19, 2.28, 4.69, 1, 20, -6.02, -1.84, 0, 2, 19, 7.61, 1.39, 0.4496, 20, -0.79, 1.6, 0.5504, 1, 20, 6.47, 3.69, 1, 2, 20, 13.38, 2.53, 0.24129, 21, 0.3, 3.04, 0.75871, 1, 21, 7.86, 3.56, 1, 2, 21, 15.49, 2.54, 0.73289, 22, 1.51, 3.28, 0.26711, 3, 21, 22.87, 0.61, 0.00346, 22, 9.13, 3.23, 0.88951, 23, -1.96, 3.4, 0.10702, 1, 23, 4.43, 2.7, 1, 1, 23, 10.98, 1.19, 1, 1, 23, 10.52, -3.15, 1, 2, 22, 14.79, -3.03, 0.0062, 23, 3.19, -3.29, 0.9938, 2, 22, 9.35, -3.04, 0.91274, 23, -2.23, -2.88, 0.08726, 1, 22, 1.84, -3.46, 1, 2, 21, 6.91, -2.97, 0.91488, 22, -5.46, -4.17, 0.08512, 2, 20, 11.16, -3.27, 0.40685, 21, 1.08, -3.13, 0.59315, 2, 19, 6.91, -6.34, 0.005, 20, 5.92, -2.3, 0.995, 3, 18, 14.08, -6.05, 0.00504, 19, 5.29, -3.33, 0.36704, 20, 2.51, -2.5, 0.62792, 3, 18, 12.67, -3.95, 0.16143, 19, 2.82, -2.77, 0.76288, 20, 0.97, -4.5, 0.07569, 2, 18, 9.31, -3, 0.88319, 19, -0.28, -4.39, 0.11681, 2, 17, 14.37, -3.91, 0.13783, 18, 2.48, -3.53, 0.86217, 1, 17, 6.81, -3.77, 1, 2, 16, 8.97, -3.74, 0.8549, 17, -1.94, -3.65, 0.1451, 2, 15, 10.59, -3.05, 0.33438, 16, -0.09, -3.5, 0.66562, 1, 15, 3.38, -4.4, 1, 2, 14, 9.23, -3.61, 0.73392, 15, -2.59, -2.58, 0.26608, 2, 13, 13.94, -1.73, 0.01753, 14, 2.1, -3.18, 0.98247, 1, 13, 6.52, -3.71, 1], "hull": 37, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0], "width": 45, "height": 43}}, "Beard10": {"Beard10": {"type": "mesh", "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "triangles": [0, 4, 6, 6, 7, 0, 0, 7, 8, 4, 5, 6, 3, 1, 2, 1, 3, 0, 3, 4, 0], "vertices": [3, 50, 24.09, -5.51, 0.00103, 51, 11.33, -6.73, 0.50207, 52, 2.29, -6.8, 0.4969, 3, 50, 11.14, -7.65, 0.65738, 51, -1.77, -7.51, 0.34258, 52, -7.16, -15.91, 5e-05, 1, 50, -3.75, -4.67, 1, 1, 50, -1.91, 4.54, 1, 2, 50, 8.19, 5.88, 0.86453, 51, -3.29, 6.26, 0.13547, 3, 50, 17.45, 10.46, 0.06726, 51, 6.4, 9.85, 0.9309, 52, -12.22, 2.6, 0.00184, 2, 51, 16.86, 6.59, 0.20796, 52, -2.15, 6.91, 0.79204, 1, 52, 16.96, 3.5, 1, 1, 52, 18.83, -0.86, 1], "hull": 9, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16], "width": 16, "height": 43}}, "Beard11": {"Beard10": {"type": "mesh", "uvs": [0.7557, 0.38983, 1, 0.66263, 1, 1, 0.5056, 1, 0.33191, 0.78583, 0, 0.60396, 0, 0.36049, 0.56118, 0, 0.81128, 0], "triangles": [0, 4, 6, 4, 5, 6, 6, 7, 0, 0, 7, 8, 3, 1, 2, 1, 3, 0, 3, 4, 0], "vertices": [3, 64, 24.3, 4.47, 0.00172, 65, 11.31, 6.36, 0.72462, 66, 2.65, 6.45, 0.27366, 2, 64, 11.46, 7.17, 0.8494, 65, -1.8, 6.97, 0.1506, 1, 64, -3.54, 4.83, 1, 2, 64, -2.1, -4.45, 0.99976, 65, -13.32, -6.67, 0.00024, 3, 64, 7.93, -6.23, 0.60869, 65, -3.13, -6.82, 0.37724, 66, -16.88, 7.37, 0.01407, 3, 64, 16.99, -11.2, 0.00207, 65, 6.61, -10.28, 0.70694, 66, -12.46, -1.97, 0.29099, 2, 65, 17.02, -6.88, 0.00812, 66, -2.7, -6.94, 0.99188, 1, 66, 16.6, -4.82, 1, 1, 66, 18.76, -0.58, 1], "hull": 9, "edges": [4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 4, 2, 0, 2, 14, 16, 0, 16], "width": 16, "height": 43}}, "Beard12": {"Beard9": {"type": "mesh", "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "triangles": [3, 0, 2, 2, 0, 1, 8, 3, 4, 8, 9, 3, 9, 0, 3, 5, 6, 4, 6, 7, 4, 7, 8, 4], "vertices": [1, 69, 9.43, -6.4, 1, 1, 69, 18.43, 0.95, 1, 2, 68, 14.83, 8.19, 0.21034, 69, 5.99, 5.59, 0.78966, 3, 67, 19.77, 8.9, 0.00727, 68, 6.86, 6.64, 0.90616, 69, -0.65, 10.27, 0.08657, 2, 67, 12.54, 6.42, 0.60053, 68, -0.75, 7.42, 0.39947, 1, 67, -3.24, 6.61, 1, 1, 67, -5.29, -0.35, 1, 1, 67, -2.39, -8.2, 1, 2, 67, 17.67, -10.12, 0.64472, 68, -3.02, -9.75, 0.35528, 3, 67, 30.48, -5.39, 0.0049, 68, 10.58, -10.83, 0.99254, 69, -10.68, -4.5, 0.00256], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 26, "height": 46}}, "Beard13": {"Beard8": {"type": "mesh", "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "triangles": [2, 0, 1, 3, 0, 2, 10, 0, 3, 4, 10, 3, 7, 8, 4, 8, 9, 10, 8, 10, 4, 7, 4, 5, 6, 7, 5], "vertices": [1, 91, 14.45, -5.16, 1, 1, 91, 20.39, -1.14, 1, 2, 90, 22.01, 16.01, 0.00045, 91, 16.86, 3.92, 0.99955, 3, 89, 17.42, 16.71, 0.0051, 90, 14.26, 10.17, 0.26762, 91, 7.61, 6.85, 0.72728, 3, 89, 11.55, 11.83, 0.18389, 90, 6.63, 9.82, 0.69065, 91, 2.9, 12.86, 0.12546, 2, 89, -2.31, 5.34, 0.98925, 90, -8.34, 13, 0.01075, 1, 89, -9.05, 0.05, 1, 1, 89, -2.43, -9.14, 1, 2, 89, 19.28, -6.47, 0.03748, 90, 1.76, -9.44, 0.96252, 2, 90, 14.5, -9.74, 0.99923, 91, -8.47, -4.88, 0.00077, 2, 90, 23.21, -0.66, 0.00033, 91, 3.97, -6.72, 0.99967], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 26, "height": 45}}, "Beard14": {"Beard7": {"type": "mesh", "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 22, "height": 59}}, "Beard15": {"Beard6": {"type": "mesh", "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "triangles": [1, 2, 0, 0, 2, 8, 7, 8, 2, 3, 5, 2, 6, 7, 2, 5, 6, 2, 4, 5, 3], "vertices": [1, 81, 16.99, -1.16, 1, 3, 79, 29.28, 15.4, 6e-05, 80, 21.01, 5, 0.00071, 81, 7.48, 4.26, 0.99923, 4, 78, 30.31, 12.23, 0.0005, 79, 16.46, 10.3, 0.40489, 80, 7.45, 7.58, 0.53199, 81, -5.74, 8.2, 0.06261, 3, 78, 18.73, 10.39, 0.22189, 79, 4.74, 9.88, 0.77794, 80, -2.65, 13.52, 0.00017, 1, 78, 0.56, 7.15, 1, 1, 78, -9.16, -1.15, 1, 1, 78, -7.11, -8.48, 1, 2, 78, 11.4, -7.47, 0.97723, 79, -4.72, -6.94, 0.02277, 2, 80, 7.57, -7.65, 0.98012, 81, -7.16, -6.96, 0.01988], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 17, "height": 66}}, "Beard16": {"Beard5": {"type": "mesh", "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "triangles": [1, 9, 0, 8, 9, 1, 2, 8, 1, 7, 8, 2, 3, 6, 7, 3, 7, 2, 6, 3, 4, 6, 4, 5], "vertices": [1, 84, 26.83, -0.93, 1, 1, 84, 11.86, 4.11, 1, 2, 83, 14.93, 9.52, 0.04938, 84, 6.55, 6.65, 0.95062, 3, 82, 17.24, 10.41, 0.02386, 83, 9.04, 7.65, 0.57746, 84, 1.61, 10.36, 0.39867, 2, 82, 3.35, 4.84, 0.99394, 83, -5.93, 7.51, 0.00606, 1, 82, -2.65, -1.9, 1, 2, 82, 7.5, -10.93, 0.98661, 83, -7.78, -8.7, 0.01339, 2, 82, 25.11, -2.28, 6e-05, 83, 11.76, -7.03, 0.99994, 2, 83, 24.05, -0.75, 0.50993, 84, 3.47, -6.74, 0.49007, 1, 84, 24.3, -5.79, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 21, "height": 50}}, "Beard17": {"Beard4": {"type": "mesh", "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "triangles": [0, 1, 10, 9, 10, 1, 2, 9, 1, 2, 8, 9, 2, 7, 8, 6, 7, 2, 3, 4, 5, 2, 3, 5, 6, 2, 5], "vertices": [2, 76, 18.83, -1.59, 0.00052, 77, 6.75, 1.09, 0.99948, 3, 74, 23.45, 12.86, 0.00387, 75, 13.22, 8.2, 0.66692, 76, 4.03, 8.16, 0.32921, 3, 74, 13.64, 8.91, 0.4155, 75, 2.64, 8.57, 0.58421, 76, -2.18, 16.73, 0.00029, 1, 74, -2.03, 6.47, 1, 1, 74, -8.62, 3.32, 1, 1, 74, -4.38, -5.56, 1, 2, 74, 7.25, -9.87, 0.99994, 75, -10.83, -5.99, 6e-05, 2, 74, 21.32, -5.65, 0.0809, 75, 3.75, -7.85, 0.9191, 2, 75, 15.45, -7.42, 0.99351, 76, -6.91, -3.21, 0.00649, 3, 75, 23.19, -0.45, 0.0085, 76, 3.35, -5.02, 0.94146, 77, -7.09, -6.66, 0.05004, 1, 77, 4.32, -2.57, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 50, "height": 21}}, "Beard18": {"Beard3": {"type": "mesh", "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "triangles": [14, 0, 1, 13, 14, 1, 11, 13, 1, 12, 13, 11, 2, 3, 10, 11, 2, 10, 1, 2, 11, 5, 3, 4, 10, 5, 6, 9, 10, 6, 7, 8, 9, 7, 9, 6, 5, 10, 3], "vertices": [1, 73, 17.38, 2.31, 1, 2, 72, 20.46, 7.4, 0.01459, 73, 8.71, 2.88, 0.98541, 2, 72, 13.33, 6.64, 0.59822, 73, 2.47, 6.4, 0.40178, 2, 71, 22.05, 5.03, 0.11799, 72, 2.09, 7.06, 0.88201, 1, 71, 10.11, 8.25, 1, 1, 71, -1.85, 4.97, 1, 1, 71, -5.24, -2.15, 1, 1, 71, -5.67, -10.99, 1, 1, 71, -0.17, -11.26, 1, 2, 71, 7.02, -7.11, 0.99225, 72, -4.72, -11.02, 0.00775, 2, 71, 12.94, -6.62, 0.7408, 72, 0.14, -7.6, 0.2592, 2, 71, 21.94, -9.86, 0.02029, 72, 9.54, -5.84, 0.97971, 2, 72, 19.21, -3.59, 0.14107, 73, 1.32, -5.35, 0.85893, 1, 73, 12.3, -5.13, 1, 1, 73, 16.75, -2.3, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 52, "height": 20}}, "Beard19": {"Beard2": {"type": "mesh", "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "triangles": [13, 4, 5, 13, 5, 7, 13, 7, 8, 6, 7, 5, 13, 8, 14, 4, 13, 3, 2, 3, 14, 3, 13, 14, 8, 9, 14, 14, 9, 10, 15, 14, 10, 10, 11, 15, 14, 15, 2, 2, 15, 1, 15, 0, 1, 15, 12, 0, 15, 11, 12], "vertices": [1, 97, -6.24, -3.22, 1, 2, 97, -4.48, -8.9, 0.99287, 98, -14.33, -12.77, 0.00713, 2, 97, 2.9, -12.8, 0.88332, 98, -6.22, -14.78, 0.11668, 2, 97, 15.2, -14.6, 0.2983, 98, 6.11, -13.53, 0.7017, 3, 97, 25.93, -11.55, 0.00548, 98, 15.79, -8, 0.70067, 99, -1.67, -7.87, 0.29386, 2, 98, 20.31, -8.28, 0.23976, 99, 2.56, -9.42, 0.76024, 1, 99, 20.52, -3.5, 1, 1, 99, 18.65, 2.34, 1, 2, 98, 21.91, 5.6, 0.00658, 99, 8.06, 3.44, 0.99342, 2, 98, 14.08, 9.05, 0.68412, 99, 1.59, 9, 0.31588, 2, 98, 3.51, 10.34, 0.99907, 99, -8.18, 13.29, 0.00093, 2, 97, 10.97, 9.08, 0.20429, 98, -3.69, 8.47, 0.79571, 2, 97, 1.21, 6.05, 0.97663, 98, -12.39, 3.14, 0.02337, 2, 98, 16.54, -0.81, 0.05319, 99, 1.08, -1.17, 0.94681, 2, 98, 6.42, 0.85, 0.99998, 99, -8.12, 3.33, 2e-05, 1, 97, 7.95, 1, 1], "hull": 13, "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0], "width": 40, "height": 21}}, "Beard2": {"Beard2": {"type": "mesh", "uvs": [1, 0.24565, 1, 0.46609, 0.88512, 0.68654, 0.65978, 0.88631, 0.43801, 0.8932, 0.36468, 1, 0, 1, 0, 0.77265, 0.18763, 0.6142, 0.29493, 0.32143, 0.46841, 0.07343, 0.60791, 0, 0.81001, 0, 0.34322, 0.69687, 0.51134, 0.43165, 0.71522, 0.25254], "triangles": [13, 8, 14, 6, 7, 5, 13, 7, 8, 13, 5, 7, 13, 4, 5, 14, 9, 10, 8, 9, 14, 3, 13, 14, 2, 3, 14, 4, 13, 3, 15, 11, 12, 15, 12, 0, 10, 11, 15, 15, 14, 10, 15, 0, 1, 2, 15, 1, 14, 15, 2], "vertices": [2, 10, -8.03, 5.17, 0.94846, 11, -18.39, 10.44, 0.05154, 2, 10, -6.02, 10.77, 0.86737, 11, -14.95, 15.3, 0.13263, 2, 10, 1.61, 14.35, 0.56962, 11, -6.63, 16.71, 0.43038, 3, 10, 14.46, 15.47, 0.02785, 11, 6.05, 14.33, 0.97201, 12, -12.79, 11.19, 0.00013, 2, 11, 15.57, 7.82, 0.57037, 12, -1.82, 7.61, 0.42963, 2, 11, 20.34, 7.97, 0.12815, 12, 2.72, 9.1, 0.87185, 1, 12, 20.65, 2.93, 1, 1, 12, 18.65, -2.88, 1, 3, 10, 35.09, 0.27, 0.00064, 11, 21.84, -5.85, 0.02287, 12, 8.04, -3.75, 0.97649, 3, 10, 27.17, -5.29, 0.12228, 11, 12.71, -9.08, 0.6045, 12, 0.19, -9.4, 0.27322, 3, 10, 16.42, -8.54, 0.88359, 11, 1.48, -9.33, 0.11615, 12, -10.52, -12.8, 0.00027, 1, 10, 8.92, -7.96, 1, 1, 10, -0.97, -4.41, 1, 2, 11, 16.52, 0.64, 0.0426, 12, 1.11, 1, 0.9574, 3, 10, 17.58, 1.31, 0.00464, 11, 5.25, -0.15, 0.99535, 12, -9.48, -2.93, 1e-05, 2, 10, 5.97, 0.34, 0.98978, 11, -6.2, 2.04, 0.01022], "hull": 13, "edges": [22, 20, 20, 18, 18, 16, 12, 14, 16, 14, 10, 12, 10, 8, 8, 6, 6, 4, 4, 2, 22, 24, 2, 0, 24, 0], "width": 40, "height": 21}}, "Beard20": {"Beard": {"type": "mesh", "uvs": [1, 0.05372, 1, 0.16485, 0.86575, 0.10674, 0.7548, 0.1018, 0.66533, 0.15125, 0.63908, 0.25139, 0.65817, 0.36885, 0.688, 0.51721, 0.72612, 0.66943, 0.73566, 0.83633, 0.70465, 0.96615, 0.59967, 1, 0.46725, 1, 0.3563, 0.94513, 0.25489, 0.85612, 0.17139, 0.74608, 0.10219, 0.62739, 0.04612, 0.52601, 0, 0.4135, 0.0664, 0.37394, 0.14395, 0.48026, 0.19405, 0.56434, 0.26921, 0.67684, 0.34675, 0.78317, 0.42191, 0.85488, 0.50542, 0.89815, 0.56388, 0.91175, 0.59967, 0.88455, 0.61398, 0.82273, 0.59967, 0.6991, 0.57342, 0.56434, 0.5424, 0.40855, 0.51974, 0.24535, 0.5436, 0.1143, 0.6116, 0.02528, 0.73447, 0, 0.86928, 0], "triangles": [16, 20, 21, 17, 18, 19, 17, 19, 20, 16, 17, 20, 23, 15, 22, 16, 21, 22, 15, 16, 22, 13, 24, 12, 23, 14, 15, 13, 23, 24, 14, 23, 13, 26, 27, 11, 12, 24, 25, 12, 25, 26, 11, 12, 26, 10, 27, 9, 11, 27, 10, 29, 30, 8, 28, 29, 8, 28, 8, 9, 9, 27, 28, 30, 31, 7, 30, 7, 8, 5, 32, 33, 31, 32, 5, 31, 5, 6, 31, 6, 7, 33, 34, 4, 4, 5, 33, 3, 35, 36, 34, 35, 3, 4, 34, 3, 2, 3, 36, 2, 36, 0, 2, 0, 1], "vertices": [1, 110, -1.48, 2.74, 1, 1, 110, -2.97, -3.19, 1, 1, 110, 5.23, -1.94, 1, 3, 110, 11.43, -3.21, 0.10215, 111, 2.1, -2.59, 0.88741, 112, -1.55, -6.89, 0.01043, 3, 111, 7.58, -4.43, 0.25554, 112, 3.05, -3.39, 0.73637, 113, -2.67, -6.18, 0.00809, 2, 112, 8.68, -4.34, 0.26602, 113, 2.42, -3.6, 0.73398, 2, 113, 8.97, -3.36, 0.8478, 114, -1.71, -3.39, 0.1522, 1, 114, 6.63, -3.27, 1, 2, 114, 15.27, -3.57, 0.05733, 115, 2.25, -3.88, 0.94267, 2, 115, 11.44, -3.57, 0.88867, 116, -3.07, -2.13, 0.11133, 1, 116, 3.88, -4.54, 1, 2, 116, 8.7, -0.54, 0.12863, 117, 0.49, -2.22, 0.87137, 1, 117, 7.99, -3.11, 1, 2, 117, 14.62, -0.86, 0.00162, 118, 1.67, -2.58, 0.99838, 2, 118, 9.18, -3.53, 0.82819, 119, -4.53, -2.54, 0.17181, 1, 119, 3.14, -3.25, 1, 2, 119, 10.76, -3.03, 0.68365, 120, -0.59, -3.03, 0.31635, 1, 120, 5.82, -2.7, 1, 1, 120, 12.45, -1.56, 1, 1, 120, 12.23, 2.8, 1, 1, 120, 4.92, 3.35, 1, 2, 119, 10.83, 3.25, 0.6417, 120, -0.51, 3.25, 0.3583, 2, 118, 15.9, 3.73, 0.40379, 119, 3.31, 3.5, 0.59621, 1, 118, 8.61, 3.04, 1, 2, 117, 11.49, 4.51, 0.21919, 118, 2.8, 3.53, 0.78081, 3, 116, 6.94, 7.01, 0.00064, 117, 6.49, 2.71, 0.95649, 118, -2.17, 5.44, 0.04286, 3, 115, 14.66, 6.56, 0.00052, 116, 5.75, 3.81, 0.19062, 117, 3.09, 2.35, 0.80887, 3, 115, 13.36, 4.39, 0.07422, 116, 3.39, 2.92, 0.74981, 117, 1.24, 4.08, 0.17598, 3, 115, 10.05, 3.26, 0.78925, 116, 0.09, 4.09, 0.20893, 117, 0.83, 7.55, 0.00182, 2, 114, 15.3, 3.82, 0.03753, 115, 3.2, 3.44, 0.96247, 2, 114, 7.74, 3.67, 0.99972, 115, -4.32, 4.24, 0.00028, 2, 113, 9.77, 3.54, 0.66442, 114, -1.01, 3.53, 0.33558, 2, 112, 11.22, 1.98, 0.14169, 113, 0.72, 2.99, 0.85831, 1, 112, 4.11, 3.76, 1, 2, 111, 9.45, 2.91, 0.64205, 112, -1.96, 2.29, 0.35795, 2, 110, 13.92, 1.94, 0.02036, 111, 2.32, 3.12, 0.97964, 1, 110, 6.46, 3.8, 1], "hull": 37, "edges": [2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 2, 0, 72, 0], "width": 45, "height": 43}}, "Beard3": {"Beard3": {"type": "mesh", "uvs": [0, 0.51115, 0.16672, 0.54215, 0.29489, 0.4104, 0.48268, 0.1314, 0.71518, 0, 0.94172, 0.1934, 1, 0.55765, 1, 1, 0.89403, 1, 0.75989, 0.77465, 0.64662, 0.7359, 0.47076, 0.8754, 0.28595, 1, 0.0773, 0.91415, 0, 0.74365], "triangles": [12, 13, 11, 11, 13, 1, 13, 14, 1, 1, 2, 11, 14, 0, 1, 11, 2, 10, 2, 3, 10, 5, 10, 3, 7, 9, 6, 7, 8, 9, 9, 10, 6, 10, 5, 6, 5, 3, 4], "vertices": [1, 45, 19.25, -3.46, 1, 1, 45, 10.59, -2.8, 1, 2, 44, 16.52, -6.59, 0.28901, 45, 3.91, -5.41, 0.71099, 2, 44, 5.29, -7.18, 0.99919, 45, -5.88, -10.95, 0.00081, 2, 43, 9.33, -7.31, 0.95661, 44, -6.69, -4.1, 0.04339, 1, 43, -2.96, -5.63, 1, 1, 43, -7.26, 0.99, 1, 1, 43, -8.86, 9.69, 1, 1, 43, -3.44, 10.68, 1, 2, 43, 4.24, 7.51, 0.9338, 44, -1.81, 10.79, 0.0662, 2, 43, 10.17, 7.81, 0.46262, 44, 3.11, 7.45, 0.53738, 3, 43, 18.66, 12.21, 0.00266, 44, 12.53, 5.83, 0.9259, 45, -5.19, 3.93, 0.07144, 1, 45, 4.43, 6.38, 1, 1, 45, 15.27, 4.62, 1, 1, 45, 19.27, 1.19, 1], "hull": 15, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 0, 28], "width": 52, "height": 20}}, "Beard4": {"Beard4": {"type": "mesh", "uvs": [0, 0, 0.33896, 0.24836, 0.55016, 0.21693, 0.85376, 0, 1, 0, 1, 0.46836, 0.82736, 0.89265, 0.53696, 1, 0.30266, 0.99479, 0.14426, 0.67265, 0, 0.20908], "triangles": [0, 1, 10, 9, 10, 1, 2, 8, 9, 2, 9, 1, 2, 7, 8, 6, 7, 2, 3, 4, 5, 2, 3, 5, 6, 2, 5], "vertices": [1, 42, 11.95, -0.2, 1, 3, 40, 17.36, -8.83, 0.18767, 41, 6.64, -6.04, 0.79469, 42, -4.56, -6.64, 0.01764, 3, 39, 15.33, -10.74, 0.07083, 40, 6.78, -8.44, 0.87911, 41, -2.02, -12.12, 0.05006, 2, 39, 0.46, -5.25, 0.99295, 40, -8.77, -11.47, 0.00705, 1, 39, -5.39, -0.86, 1, 1, 39, 0.52, 7, 1, 2, 39, 12.78, 8.94, 0.84505, 40, -5.6, 7.05, 0.15495, 1, 40, 9.07, 7.86, 1, 2, 40, 20.71, 6.59, 0.33855, 41, 0.01, 8.28, 0.66145, 2, 41, 10.3, 6.64, 0.8358, 42, -2.48, 6.39, 0.1642, 2, 41, 21.53, 2.1, 0.0004, 42, 9.22, 3.24, 0.9996], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 50, "height": 21}}, "Beard5": {"Beard5": {"type": "mesh", "uvs": [0.75898, 0, 0.64298, 0.31206, 0.63331, 0.4298, 0.68165, 0.5516, 1, 0.81956, 1, 1, 0.35298, 1, 0, 0.63686, 0, 0.36078, 0.49798, 0], "triangles": [2, 8, 1, 8, 9, 1, 1, 9, 0, 3, 7, 2, 3, 6, 7, 7, 8, 2, 6, 4, 5, 6, 3, 4], "vertices": [1, 38, 25.36, -2.76, 1, 2, 37, 20.6, -10.53, 0.19231, 38, 9.91, -6.02, 0.80769, 2, 37, 15.05, -8.57, 0.79966, 38, 4.33, -7.92, 0.20034, 2, 36, 16.69, -10.64, 0.02874, 37, 8.93, -7.71, 0.97126, 2, 36, 2.2, -6.87, 0.93141, 37, -5.85, -10.06, 0.06859, 1, 36, -4.59, -0.94, 1, 2, 36, 4.34, 9.3, 0.99794, 37, -10.38, 5.61, 0.00206, 2, 37, 9.17, 7.23, 0.79006, 38, -10.06, 0.84, 0.20994, 1, 38, 2.84, 5.74, 1, 1, 38, 23.42, 2.36, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 21, "height": 50}}, "Beard6": {"Beard6": {"type": "mesh", "uvs": [1, 0, 1, 0.16578, 0.81606, 0.36942, 0.89512, 0.5459, 1, 0.82421, 0.68429, 1, 0.2363, 1, 0, 0.72578, 0, 0.27439], "triangles": [1, 2, 0, 3, 5, 2, 7, 8, 2, 0, 2, 8, 4, 5, 3, 5, 6, 2, 6, 7, 2], "vertices": [1, 35, 17.53, 1.89, 1, 1, 35, 8.6, -4.43, 1, 2, 34, 6.86, -7.72, 0.93099, 35, -4.18, -9.65, 0.06901, 3, 32, 22.68, -9.86, 0.15165, 33, 6.62, -10.44, 0.3642, 34, -3.97, -12.22, 0.48415, 3, 32, 4.4, -12.37, 0.99661, 33, -11.81, -11.44, 2e-05, 34, -21.13, -18.98, 0.00337, 1, 32, -7.4, -7.46, 1, 1, 32, -7.7, 0.15, 1, 2, 32, 10.22, 4.88, 0.97997, 33, -4.6, 5.27, 0.02003, 3, 33, 25.17, 4.01, 0.12776, 34, 9.07, 7.34, 0.84511, 35, -7.07, 5.3, 0.02713], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 17, "height": 66}}, "Beard7": {"Beard7": {"type": "mesh", "uvs": [1, 0, 1, 0.15434, 0.73464, 0.32773, 0.70464, 0.4368, 0.75714, 0.55706, 1, 0.81994, 0.89964, 1, 0.47964, 1, 0, 0.63816, 0, 0.32773, 0.35964, 0.15714], "triangles": [0, 2, 10, 1, 2, 0, 3, 10, 2, 8, 9, 10, 8, 10, 3, 7, 8, 3, 7, 3, 4, 6, 7, 4, 5, 6, 4], "vertices": [1, 28, 47.27, -25.24, 1, 1, 28, 38.72, -22.1, 1, 1, 28, 31.13, -13.1, 1, 1, 28, 25.31, -10.27, 1, 1, 28, 18.25, -8.91, 1, 1, 28, 1.85, -8.6, 1, 1, 28, -7.37, -2.87, 1, 1, 28, -4.19, 5.81, 1, 1, 28, 19.49, 8.37, 1, 1, 28, 36.68, 2.07, 1, 1, 28, 43.41, -8.82, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 22, "height": 59}}, "Beard8": {"Beard8": {"type": "mesh", "uvs": [0.72434, 0, 1, 0, 0.99665, 0.13717, 0.76519, 0.3063, 0.74476, 0.47544, 0.85369, 0.80977, 0.84007, 1, 0.40438, 1, 0, 0.57377, 0, 0.29057, 0.35672, 0.10177], "triangles": [4, 10, 3, 10, 0, 3, 3, 0, 2, 2, 0, 1, 8, 10, 4, 8, 9, 10, 6, 7, 5, 7, 4, 5, 7, 8, 4], "vertices": [1, 27, 15.56, 3.44, 1, 1, 27, 21.04, -1.19, 1, 2, 26, 22.75, -16.22, 0.0024, 27, 16.99, -5.85, 0.9976, 3, 25, 25.13, -16.16, 0.03491, 26, 14.72, -10.78, 0.28124, 27, 7.48, -7.77, 0.68385, 3, 25, 18.27, -12.83, 0.33141, 26, 7.09, -10.81, 0.55855, 27, 2.16, -13.24, 0.11004, 2, 25, 3.26, -9.84, 0.99582, 26, -7.71, -14.75, 0.00418, 1, 25, -4.55, -6.32, 1, 1, 25, -0.33, 4.19, 1, 2, 25, 21.39, 6.79, 0.12632, 26, 1.25, 8.17, 0.87368, 2, 26, 13.96, 9.11, 0.72028, 27, -7.26, 5.61, 0.27972, 1, 27, 5.31, 6.11, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 26, "height": 45}}, "Beard9": {"Beard9": {"type": "mesh", "uvs": [0.55322, 0, 1, 0, 0.74222, 0.24908, 0.65822, 0.41921, 0.66522, 0.58539, 0.88222, 0.90586, 0.65822, 1, 0.33622, 1, 0, 0.60517, 0, 0.30843], "triangles": [3, 0, 2, 2, 0, 1, 8, 3, 4, 8, 9, 3, 9, 0, 3, 5, 6, 4, 6, 7, 4, 7, 8, 4], "vertices": [1, 49, 10.57, 6.75, 1, 1, 49, 18.84, -1.41, 1, 2, 48, 15.89, -7.65, 0.12763, 49, 6.02, -4.86, 0.87237, 3, 47, 20.27, -9.07, 0.02829, 48, 7.96, -5.87, 0.86755, 49, -1.03, -8.9, 0.10416, 3, 47, 13.39, -5.75, 0.64084, 48, 0.34, -6.45, 0.35912, 49, -6.27, -14.47, 4e-05, 1, 47, -2.31, -4.05, 1, 1, 47, -3.51, 3.11, 1, 1, 47, 0.31, 10.56, 1, 2, 47, 20.46, 10.06, 0.38978, 48, -1.47, 10.77, 0.61022, 3, 47, 32.61, 3.84, 0.0002, 48, 12.16, 11.48, 0.9782, 49, -9.64, 6.75, 0.02159], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 26, "height": 46}}, "Bread_Mouth": {"Bread_Mouth": {"x": 12.08, "y": 0.67, "rotation": 101.48, "width": 11, "height": 29}}, "Bread_Mouth10": {"Bread_Mouth3": {"x": 6.94, "y": -1.59, "rotation": 89.2, "width": 13, "height": 20}}, "Bread_Mouth2": {"Bread_Mouth2": {"x": 9.15, "y": 1.16, "rotation": 85.76, "width": 13, "height": 23}}, "Bread_Mouth3": {"Bread_Mouth3": {"x": 6.94, "y": -1.59, "rotation": 89.2, "width": 13, "height": 20}}, "Bread_Mouth4": {"Bread_Mouth4": {"x": 8.56, "y": 0.46, "rotation": 100.97, "width": 11, "height": 28}}, "Bread_Mouth5": {"Bread_Mouth5": {"type": "mesh", "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "triangles": [5, 6, 4, 4, 6, 3, 6, 2, 3, 6, 7, 2, 7, 1, 2, 7, 8, 1, 1, 8, 0, 8, 9, 0], "vertices": [1, 54, -3.19, -5.92, 1, 1, 54, 0.34, 4.29, 1, 2, 54, 14.15, 3.94, 0.83886, 55, -3.27, 2.55, 0.16114, 2, 55, 10.18, 6.14, 0.4471, 56, -1.92, 5.98, 0.5529, 1, 56, 11.45, 5.24, 1, 1, 56, 15, 1.7, 1, 3, 54, 20.32, -9.69, 0.04355, 55, 9.48, -5.29, 0.19309, 56, 3.21, -4.25, 0.76336, 3, 54, 14.52, -8.29, 0.50163, 55, 3.88, -7.38, 0.39679, 56, -0.58, -8.87, 0.10159, 3, 54, 6.27, -7.74, 0.98769, 55, -3.26, -11.54, 0.01226, 56, -4.67, -16.05, 5e-05, 1, 54, 1.98, -7.71, 1], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 15, "height": 36}}, "Bread_Mouth6": {"Bread_Mouth5": {"type": "mesh", "uvs": [0, 0, 0.72059, 0, 1, 0.36558, 1, 0.75225, 0.33393, 1, 0, 1, 0.27593, 0.65155, 0.23726, 0.48641, 0.09226, 0.26489, 0, 0.15211], "triangles": [6, 2, 3, 4, 6, 3, 5, 6, 4, 8, 9, 0, 1, 8, 0, 7, 8, 1, 7, 1, 2, 6, 7, 2], "vertices": [1, 100, -3.72, 4.95, 1, 1, 100, 0.26, -5.1, 1, 2, 100, 14.04, -4.14, 0.97271, 101, -3.38, -2.77, 0.02729, 2, 101, 10.3, -5.31, 0.24012, 102, -2.08, -5.1, 0.75988, 1, 102, 11.31, -5.08, 1, 1, 102, 15.04, -1.74, 1, 3, 100, 19.61, 9.75, 0.02206, 101, 8.73, 6.03, 0.42154, 102, 3.59, 4.84, 0.5564, 3, 100, 13.87, 8.09, 0.37297, 101, 2.99, 7.68, 0.59291, 102, 0.06, 9.66, 0.03412, 2, 100, 5.65, 7.18, 0.94416, 101, -4.46, 11.28, 0.05584, 2, 100, 1.37, 6.97, 0.99525, 101, -8.19, 13.38, 0.00475], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 15, "height": 36}}, "Bread_Mouth7": {"Bread_Mouth4": {"x": 7.74, "y": 0.8, "rotation": 95.48, "width": 11, "height": 28}}, "Bread_Mouth8": {"Bread_Mouth": {"x": 11.13, "y": 1.16, "rotation": 101.48, "width": 11, "height": 29}}, "Bread_Mouth9": {"Bread_Mouth2": {"x": 9.15, "y": 1.16, "rotation": 85.76, "width": 13, "height": 23}}, "Eye": {"Eye": {"x": 0.5, "y": -0.18, "width": 14, "height": 13}}, "Eye2": {"Eye": {"x": 0.5, "y": -0.18, "scaleX": -1, "width": 14, "height": 13}}, "Eyebrow": {"Eyebrow": {"type": "mesh", "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "triangles": [4, 1, 2, 5, 0, 1, 3, 4, 2, 4, 5, 1], "vertices": [2, 7, 27.49, 8.37, 5e-05, 8, 15.65, 4.86, 0.99995, 1, 8, 13.53, -5.37, 1, 3, 6, 21.49, -10.09, 0.01048, 7, 11.29, -9.86, 0.91242, 8, -4.35, -9.1, 0.0771, 2, 6, 1.8, -10.3, 0.89053, 7, -8.39, -10.47, 0.10947, 1, 6, -6.85, -1.56, 1, 1, 6, -6.94, 7.61, 1], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 14, "height": 36}}, "Eyebrow2": {"Eyebrow": {"type": "mesh", "uvs": [0.0149, 0, 0.59545, 0, 1, 0.37229, 1, 0.80984, 0.5099, 1, 0, 1], "triangles": [5, 0, 1, 4, 1, 2, 4, 5, 1, 3, 4, 2], "vertices": [1, 95, 16.7, -3.51, 1, 1, 95, 14.18, 6.63, 1, 2, 94, 11.07, 10.13, 0.69008, 95, -3.84, 9.66, 0.30992, 2, 93, 3.85, 9.97, 0.98776, 94, -8.54, 8.33, 0.01224, 1, 93, -5.74, 2.29, 1, 1, 93, -6.89, -6.82, 1], "hull": 6, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 0, 10], "width": 14, "height": 36}}, "Fang": {"Fang": {"x": 10.96, "y": -2.51, "rotation": 89.2, "width": 20, "height": 21}}, "Fang2": {"Fang": {"x": 11.02, "y": 1.29, "scaleX": -1, "rotation": 89.2, "width": 20, "height": 21}}, "Frame": {"Frame": {"x": -67.12, "width": 6, "height": 120}}, "Frame2": {"Frame": {"x": 67.04, "scaleX": -1, "width": 6, "height": 120}}, "Frame_Bottom": {"Frame_Bottom": {"x": -33.63, "y": -59.78, "width": 73, "height": 6}}, "Frame_Bottom2": {"Frame_Bottom": {"x": 33.62, "y": -59.78, "scaleX": -1, "width": 73, "height": 6}}, "Frame_Top": {"Frame_Top": {"x": -33.72, "y": 58.01, "width": 73, "height": 6}}, "Frame_Top2": {"Frame_Top": {"x": 33.72, "y": 58.01, "scaleX": -1, "width": 73, "height": 6}}, "Head": {"Head": {"type": "mesh", "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024, 0.69262, 0.78202, 0.5988, 0.82848, 0.56384, 0.3732], "triangles": [21, 22, 27, 20, 21, 19, 2, 0, 1, 3, 0, 2, 18, 19, 25, 27, 23, 24, 27, 22, 23, 27, 24, 0, 21, 25, 19, 60, 27, 0, 3, 60, 0, 32, 60, 3, 32, 3, 4, 41, 4, 5, 32, 4, 41, 28, 27, 60, 28, 60, 32, 21, 27, 28, 40, 21, 28, 42, 5, 6, 7, 42, 6, 41, 5, 42, 36, 21, 40, 39, 32, 41, 28, 32, 39, 40, 28, 39, 31, 41, 42, 26, 25, 21, 26, 21, 36, 30, 31, 42, 35, 40, 39, 36, 40, 35, 34, 39, 53, 41, 37, 39, 35, 39, 34, 42, 7, 8, 30, 42, 8, 41, 31, 37, 37, 31, 30, 17, 25, 26, 18, 25, 17, 37, 53, 39, 38, 37, 30, 54, 53, 37, 35, 47, 26, 35, 26, 36, 17, 26, 47, 50, 35, 34, 47, 35, 50, 29, 30, 8, 29, 38, 30, 55, 54, 37, 55, 37, 38, 8, 9, 29, 16, 17, 47, 53, 51, 50, 53, 50, 34, 51, 53, 54, 52, 54, 55, 51, 54, 52, 33, 55, 38, 33, 38, 29, 48, 16, 47, 58, 51, 52, 43, 33, 29, 50, 48, 47, 49, 50, 51, 49, 48, 50, 33, 44, 52, 33, 52, 55, 44, 33, 43, 58, 52, 44, 59, 49, 51, 59, 51, 58, 29, 9, 10, 43, 29, 10, 15, 16, 48, 15, 48, 49, 45, 59, 58, 45, 58, 44, 57, 43, 10, 46, 49, 59, 56, 45, 44, 57, 44, 43, 12, 44, 57, 56, 44, 12, 57, 10, 11, 12, 57, 11, 14, 49, 46, 15, 49, 14, 45, 46, 59, 56, 46, 45, 56, 13, 46, 14, 46, 13, 13, 56, 12], "vertices": [2, 4, -27.12, 18.58, 0.508, 5, -27.12, 103.58, 0.492, 2, 4, -21.12, 31.66, 0.476, 5, -21.12, 116.66, 0.524, 2, 4, -15.72, 29.14, 0.492, 5, -15.72, 114.14, 0.508, 2, 4, -20.52, 10.3, 0.5, 5, -20.52, 95.3, 0.5, 2, 4, -13.32, 5.74, 0.508, 5, -13.32, 90.74, 0.492, 2, 4, -5.64, 5.74, 0.5, 5, -5.64, 90.74, 0.5, 2, 4, 2.34, -0.38, 0.524, 5, 2.34, 84.62, 0.476, 2, 4, 2.34, -4.68, 0.532, 5, 2.34, 80.32, 0.468, 2, 4, 2.34, -13.2, 0.55, 5, 2.34, 71.8, 0.45, 2, 4, 2.34, -19.44, 0.57, 5, 2.34, 65.56, 0.43, 2, 4, 2.34, -32.39, 0.59, 5, 2.34, 52.61, 0.41, 2, 4, 2.34, -41.75, 0.59, 5, 2.34, 43.25, 0.41, 2, 4, -7.44, -41.39, 0.564, 5, -7.44, 43.61, 0.436, 2, 4, -17.28, -44.96, 0.524, 5, -17.28, 40.04, 0.476, 2, 4, -28.8, -42.95, 0.508, 5, -28.8, 42.05, 0.492, 2, 4, -36.36, -33.11, 0.524, 5, -36.36, 51.89, 0.476, 2, 4, -36, -21.47, 0.548, 5, -36, 63.53, 0.452, 2, 4, -36.36, -14.73, 0.548, 5, -36.36, 70.27, 0.452, 2, 4, -56.66, 8.19, 0.46, 5, -56.66, 93.19, 0.54, 2, 4, -56.66, 13.78, 0.46, 5, -56.66, 98.78, 0.54, 2, 4, -56.66, 18.63, 0.484, 5, -56.66, 103.63, 0.516, 2, 4, -31.32, 0.15, 0.508, 5, -31.32, 85.15, 0.492, 2, 4, -47.4, 38.04, 0.468, 5, -47.4, 123.04, 0.532, 2, 4, -42.36, 38.04, 0.452, 5, -42.36, 123.04, 0.548, 2, 4, -38.64, 38.04, 0.452, 5, -38.64, 123.04, 0.548, 2, 4, -39.12, -5.3, 0.524, 5, -39.12, 79.7, 0.476, 2, 4, -33.36, -10.94, 0.54, 5, -33.36, 74.06, 0.46, 2, 4, -30.36, 7.3, 0.492, 5, -30.36, 92.3, 0.508, 2, 4, -25.56, -1.1, 0.516, 5, -25.56, 83.9, 0.484, 2, 4, -4.86, -20.64, 0.548, 5, -4.86, 64.36, 0.452, 2, 4, -5.22, -12.6, 0.564, 5, -5.22, 72.4, 0.436, 2, 4, -9.18, -7.92, 0.564, 5, -9.18, 77.08, 0.436, 2, 4, -21.06, 0, 0.524, 5, -21.06, 85, 0.476, 2, 4, -10.26, -23.16, 0.556, 5, -10.26, 61.84, 0.444, 2, 4, -19.86, -13.08, 0.556, 5, -19.86, 71.92, 0.444, 2, 4, -24.18, -12.6, 0.54, 5, -24.18, 72.4, 0.46, 2, 4, -28.98, -6, 0.532, 5, -28.98, 79, 0.468, 2, 4, -11.22, -13.32, 0.564, 5, -11.22, 71.68, 0.436, 2, 4, -7.02, -17.64, 0.564, 5, -7.02, 67.36, 0.436, 2, 4, -20.58, -6.72, 0.532, 5, -20.58, 78.28, 0.468, 2, 4, -26.46, -4.2, 0.532, 5, -26.46, 80.8, 0.468, 2, 4, -11.34, -1.08, 0.556, 5, -11.34, 83.92, 0.444, 2, 4, -3.3, -4.92, 0.548, 5, -3.3, 80.08, 0.452, 2, 4, -7.98, -28.43, 0.564, 5, -7.98, 56.57, 0.436, 2, 4, -13.02, -30.23, 0.548, 5, -13.02, 54.77, 0.452, 2, 4, -16.14, -33.95, 0.54, 5, -16.14, 51.05, 0.46, 2, 4, -23.46, -36.47, 0.54, 5, -23.46, 48.53, 0.46, 2, 4, -31.14, -18.83, 0.556, 5, -31.14, 66.17, 0.444, 2, 4, -31.38, -25.67, 0.556, 5, -31.38, 59.33, 0.444, 2, 4, -28.86, -29.15, 0.54, 5, -28.86, 55.85, 0.46, 2, 4, -22.5, -18.83, 0.548, 5, -22.5, 66.17, 0.452, 2, 4, -20.58, -22.19, 0.54, 5, -20.58, 62.81, 0.46, 2, 4, -14.94, -23.03, 0.54, 5, -14.94, 61.97, 0.46, 2, 4, -17.34, -15.23, 0.564, 5, -17.34, 69.77, 0.436, 2, 4, -15.18, -18.47, 0.548, 5, -15.18, 66.53, 0.452, 2, 4, -12.54, -20.27, 0.556, 5, -12.54, 64.73, 0.444, 2, 4, -14.88, -39.17, 0.532, 5, -14.88, 45.83, 0.468, 2, 4, -5.21, -35.85, 0.54, 5, -5.21, 49.15, 0.46, 2, 4, -15.8, -26.87, 0.53939, 5, -15.8, 58.13, 0.46061, 2, 4, -21.33, -30.72, 0.55291, 5, -21.33, 54.28, 0.44709, 2, 4, -23.39, 7.06, 0.5186, 5, -23.39, 92.06, 0.4814], "hull": 25, "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 44, "height": 65}}, "Head2": {"Head": {"type": "mesh", "uvs": [0.50065, 0.2344, 0.60234, 0.07681, 0.69387, 0.10717, 0.61251, 0.33416, 0.73455, 0.3891, 0.86472, 0.3891, 1, 0.46284, 1, 0.51466, 1, 0.61731, 1, 0.69249, 1, 0.84859, 1, 0.96135, 0.83421, 0.95701, 0.66743, 1, 0.47217, 0.9758, 0.34404, 0.85725, 0.35014, 0.71701, 0.34404, 0.63576, 0, 0.35962, 0, 0.29223, 0, 0.23383, 0.42946, 0.45649, 0.15692, 0, 0.24234, 0, 0.3054, 0, 0.29726, 0.52211, 0.39489, 0.59007, 0.44573, 0.37031, 0.52709, 0.47151, 0.87801, 0.70695, 0.8719, 0.61008, 0.80479, 0.5537, 0.60343, 0.45827, 0.78648, 0.73731, 0.62377, 0.61586, 0.55055, 0.61008, 0.46919, 0.53056, 0.77021, 0.61876, 0.8414, 0.6708, 0.61157, 0.53924, 0.51191, 0.50888, 0.76818, 0.47129, 0.90445, 0.51755, 0.82513, 0.80088, 0.7397, 0.82257, 0.68682, 0.86739, 0.56275, 0.89775, 0.43258, 0.68522, 0.42852, 0.76763, 0.47123, 0.80955, 0.57902, 0.68522, 0.61157, 0.7257, 0.70716, 0.73582, 0.66648, 0.64184, 0.70309, 0.68088, 0.74784, 0.70257, 0.70822, 0.93026, 0.87201, 0.89024], "triangles": [18, 25, 17, 21, 25, 19, 20, 21, 19, 18, 19, 25, 27, 23, 24, 27, 24, 0, 27, 22, 23, 21, 22, 27, 27, 0, 3, 32, 3, 4, 26, 21, 36, 17, 25, 26, 16, 17, 47, 17, 26, 47, 37, 53, 39, 34, 39, 53, 13, 56, 12, 14, 46, 13, 56, 46, 45, 56, 13, 46, 15, 49, 14, 14, 49, 46, 12, 57, 11, 57, 10, 11, 56, 44, 12, 56, 45, 44, 45, 46, 51, 57, 43, 10, 46, 49, 51, 45, 52, 44, 45, 51, 52, 15, 48, 49, 15, 16, 48, 43, 29, 10, 29, 9, 10, 44, 33, 43, 33, 52, 55, 33, 44, 52, 49, 48, 50, 49, 50, 51, 50, 48, 47, 43, 33, 29, 48, 16, 47, 33, 38, 29, 33, 55, 38, 51, 54, 52, 52, 54, 55, 51, 53, 54, 53, 50, 34, 53, 51, 50, 8, 9, 29, 55, 37, 38, 55, 54, 37, 29, 38, 30, 29, 30, 8, 47, 35, 50, 50, 35, 34, 35, 26, 36, 35, 47, 26, 54, 53, 37, 38, 37, 30, 37, 31, 30, 41, 31, 37, 30, 42, 8, 42, 7, 8, 35, 39, 34, 41, 37, 39, 36, 40, 35, 35, 40, 39, 30, 31, 42, 31, 41, 42, 40, 28, 39, 28, 32, 39, 39, 32, 41, 41, 5, 42, 7, 42, 6, 42, 5, 6, 3, 32, 28, 32, 4, 41, 41, 4, 5, 3, 0, 2, 2, 0, 1, 12, 44, 57, 57, 44, 43, 26, 25, 21, 36, 21, 40, 40, 21, 28, 28, 21, 27, 3, 28, 27], "vertices": [2, 4, 28.87, 18.58, 0.5, 5, 28.87, 103.58, 0.5, 2, 4, 22.87, 31.66, 0.5, 5, 22.87, 116.66, 0.5, 2, 4, 17.47, 29.14, 0.5, 5, 17.47, 114.14, 0.5, 2, 4, 22.27, 10.3, 0.5, 5, 22.27, 95.3, 0.5, 2, 4, 15.07, 5.74, 0.5, 5, 15.07, 90.74, 0.5, 2, 4, 7.39, 5.74, 0.5, 5, 7.39, 90.74, 0.5, 2, 4, -0.59, -0.38, 0.524, 5, -0.59, 84.62, 0.476, 2, 4, -0.59, -4.68, 0.53, 5, -0.59, 80.32, 0.47, 2, 4, -0.59, -13.2, 0.55, 5, -0.59, 71.8, 0.45, 2, 4, -0.59, -19.44, 0.57, 5, -0.59, 65.56, 0.43, 2, 4, -0.59, -32.39, 0.59, 5, -0.59, 52.61, 0.41, 2, 4, -0.59, -41.75, 0.59, 5, -0.59, 43.25, 0.41, 2, 4, 9.19, -41.39, 0.564, 5, 9.19, 43.61, 0.436, 2, 4, 19.03, -44.96, 0.53, 5, 19.03, 40.04, 0.47, 2, 4, 30.55, -42.95, 0.51, 5, 30.55, 42.05, 0.49, 2, 4, 38.11, -33.11, 0.53, 5, 38.11, 51.89, 0.47, 2, 4, 37.75, -21.47, 0.5, 5, 37.75, 63.53, 0.5, 2, 4, 38.11, -14.73, 0.5, 5, 38.11, 70.27, 0.5, 2, 4, 58.41, 8.19, 0.46, 5, 58.41, 93.19, 0.54, 2, 4, 58.41, 13.78, 0.46, 5, 58.41, 98.78, 0.54, 2, 4, 58.41, 18.63, 0.48, 5, 58.41, 103.63, 0.52, 2, 4, 33.07, 0.15, 0.492, 5, 33.07, 85.15, 0.508, 2, 4, 49.15, 38.04, 0.45, 5, 49.15, 123.04, 0.55, 2, 4, 44.11, 38.04, 0.45, 5, 44.11, 123.04, 0.55, 2, 4, 40.39, 38.04, 0.46, 5, 40.39, 123.04, 0.54, 2, 4, 40.87, -5.3, 0.516, 5, 40.87, 79.7, 0.484, 2, 4, 35.11, -10.94, 0.524, 5, 35.11, 74.06, 0.476, 2, 4, 32.11, 7.3, 0.492, 5, 32.11, 92.3, 0.508, 2, 4, 27.31, -1.1, 0.516, 5, 27.31, 83.9, 0.484, 2, 4, 6.61, -20.64, 0.548, 5, 6.61, 64.36, 0.452, 2, 4, 6.97, -12.6, 0.564, 5, 6.97, 72.4, 0.436, 2, 4, 10.93, -7.92, 0.564, 5, 10.93, 77.08, 0.436, 2, 4, 22.81, 0, 0.516, 5, 22.81, 85, 0.484, 2, 4, 12.01, -23.16, 0.556, 5, 12.01, 61.84, 0.444, 2, 4, 21.61, -13.08, 0.56, 5, 21.61, 71.92, 0.44, 2, 4, 25.93, -12.6, 0.54, 5, 25.93, 72.4, 0.46, 2, 4, 30.73, -6, 0.524, 5, 30.73, 79, 0.476, 2, 4, 12.97, -13.32, 0.564, 5, 12.97, 71.68, 0.436, 2, 4, 8.77, -17.64, 0.564, 5, 8.77, 67.36, 0.436, 2, 4, 22.33, -6.72, 0.532, 5, 22.33, 78.28, 0.468, 2, 4, 28.21, -4.2, 0.524, 5, 28.21, 80.8, 0.476, 2, 4, 13.09, -1.08, 0.55, 5, 13.09, 83.92, 0.45, 2, 4, 5.05, -4.92, 0.54, 5, 5.05, 80.08, 0.46, 2, 4, 9.73, -28.43, 0.56, 5, 9.73, 56.57, 0.44, 2, 4, 14.77, -30.23, 0.548, 5, 14.77, 54.77, 0.452, 2, 4, 17.89, -33.95, 0.54, 5, 17.89, 51.05, 0.46, 2, 4, 25.21, -36.47, 0.54, 5, 25.21, 48.53, 0.46, 2, 4, 32.89, -18.83, 0.54, 5, 32.89, 66.17, 0.46, 2, 4, 33.13, -25.67, 0.528, 5, 33.13, 59.33, 0.472, 2, 4, 30.61, -29.15, 0.54, 5, 30.61, 55.85, 0.46, 2, 4, 24.25, -18.83, 0.55, 5, 24.25, 66.17, 0.45, 2, 4, 22.33, -22.19, 0.538, 5, 22.33, 62.81, 0.462, 2, 4, 16.69, -23.03, 0.54, 5, 16.69, 61.97, 0.46, 2, 4, 19.09, -15.23, 0.548, 5, 19.09, 69.77, 0.452, 2, 4, 16.93, -18.47, 0.548, 5, 16.93, 66.53, 0.452, 2, 4, 14.29, -20.27, 0.556, 5, 14.29, 64.73, 0.444, 2, 4, 16.63, -39.17, 0.53, 5, 16.63, 45.83, 0.47, 2, 4, 6.96, -35.85, 0.54, 5, 6.96, 49.15, 0.46], "hull": 25, "edges": [40, 42, 42, 44, 36, 34, 34, 32, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 48, 0, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 44, 46, 46, 48, 36, 38, 38, 40, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22], "width": 44, "height": 65}}, "Mouth": {"Mouth": {"type": "mesh", "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.3227, 0.36013, 0.68741, 0.4459, 0.58912, 0.77631, 0.86769, 0.75164], "triangles": [9, 10, 11, 14, 27, 19, 8, 26, 14, 26, 9, 21, 14, 26, 27, 8, 9, 26, 26, 21, 27, 6, 17, 5, 5, 18, 4, 5, 17, 18, 6, 7, 16, 7, 15, 16, 6, 16, 17, 3, 4, 2, 4, 18, 25, 4, 25, 2, 17, 23, 18, 17, 16, 23, 25, 1, 2, 23, 24, 18, 18, 29, 25, 18, 24, 29, 16, 28, 23, 23, 28, 24, 16, 15, 28, 25, 29, 1, 28, 19, 24, 24, 19, 29, 19, 28, 14, 7, 8, 15, 1, 29, 20, 29, 19, 20, 1, 20, 0, 0, 27, 22, 28, 15, 14, 15, 8, 14, 19, 27, 20, 0, 20, 27, 27, 21, 22, 22, 13, 0, 21, 12, 22, 9, 11, 21, 21, 11, 12, 22, 12, 13], "vertices": [2, 4, 0.84, -53.67, 0.53198, 5, 0.84, 31.33, 0.46802, 3, 4, 0.84, -81.28, 0.46542, 5, 0.84, 3.72, 0.32258, 109, 0.84, 8.72, 0.212, 3, 4, 0.84, -87.9, 0.466, 5, 0.84, -2.9, 0.30508, 109, 0.84, 2.1, 0.22892, 3, 4, 0.84, -91.56, 0.45338, 5, 0.84, -6.56, 0.31312, 109, 0.84, -1.56, 0.2335, 3, 4, -2.26, -91.56, 0.44248, 5, -2.26, -6.56, 0.30731, 109, -2.26, -1.56, 0.25021, 3, 4, -5.19, -93.37, 0.4358, 5, -5.19, -8.37, 0.30054, 109, -5.19, -3.37, 0.26366, 3, 4, -9.46, -93.37, 0.41603, 5, -9.46, -8.37, 0.34786, 109, -9.46, -3.37, 0.23611, 3, 4, -18, -80.05, 0.45, 5, -18, 4.95, 0.36046, 109, -18, 9.95, 0.18954, 3, 4, -17.39, -66.85, 0.44, 5, -17.39, 18.15, 0.40533, 109, -17.39, 23.15, 0.15467, 3, 4, -20.16, -51.86, 0.454, 5, -20.16, 33.14, 0.46091, 109, -20.16, 38.14, 0.08509, 2, 4, -20.16, -43.49, 0.5, 5, -20.16, 41.51, 0.5, 2, 4, -12.88, -44.18, 0.5, 5, -12.88, 40.82, 0.5, 2, 4, -4.95, -42.98, 0.5, 5, -4.95, 42.02, 0.5, 2, 4, 0.84, -43.42, 0.508, 5, 0.84, 41.58, 0.492, 3, 4, -12.27, -68.9, 0.44465, 5, -12.27, 16.1, 0.32928, 109, -12.27, 21.1, 0.22607, 3, 4, -13.37, -79.62, 0.45913, 5, -13.37, 5.38, 0.36087, 109, -13.37, 10.38, 0.18, 3, 4, -11.17, -85.19, 0.4511, 5, -11.17, -0.19, 0.35967, 109, -11.17, 4.81, 0.18923, 3, 4, -7.63, -88.55, 0.43761, 5, -7.63, -3.55, 0.32116, 109, -7.63, 1.45, 0.24123, 3, 4, -4.22, -87.33, 0.44829, 5, -4.22, -2.33, 0.31529, 109, -4.22, 2.67, 0.23643, 3, 4, -5.68, -76.3, 0.498, 5, -5.68, 8.7, 0.38138, 109, -5.68, 13.7, 0.12062, 3, 4, -4.95, -73.37, 0.45268, 5, -4.95, 11.63, 0.30896, 109, -4.95, 16.63, 0.23836, 3, 4, -12.88, -52.3, 0.474, 5, -12.88, 32.7, 0.43036, 109, -12.88, 37.7, 0.09564, 3, 4, -4.09, -52.94, 0.474, 5, -4.09, 32.06, 0.43036, 109, -4.09, 37.06, 0.09564, 3, 4, -7.5, -85.05, 0.45479, 5, -7.5, -0.05, 0.32833, 109, -7.5, 4.95, 0.21688, 3, 4, -4.86, -82.68, 0.46112, 5, -4.86, 2.32, 0.33111, 109, -4.86, 7.32, 0.20777, 3, 4, -0.77, -85.46, 0.45898, 5, -0.77, -0.46, 0.32041, 109, -0.77, 4.54, 0.22062, 3, 4, -13.39, -60.81, 0.424, 5, -13.39, 24.19, 0.40533, 109, -13.39, 29.19, 0.17067, 3, 4, -5.73, -66.12, 0.45193, 5, -5.73, 18.88, 0.32447, 109, -5.73, 23.88, 0.2236, 3, 4, -7.79, -82.48, 0.46304, 5, -7.79, 2.52, 0.32404, 109, -7.79, 7.52, 0.21292, 3, 4, -1.94, -82.28, 0.46591, 5, -1.94, 2.72, 0.33414, 109, -1.94, 7.72, 0.19995], "hull": 14, "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26], "width": 17, "height": 36}}, "Mouth2": {"Mouth": {"type": "mesh", "uvs": [1, 0.19466, 1, 0.73534, 1, 0.86566, 1, 0.94884, 0.85238, 0.94884, 0.71295, 1, 0.50962, 1, 0.10296, 0.75752, 0.132, 0.47748, 0, 0.1808, 0, 0, 0.34695, 0, 0.72457, 0, 1, 0, 0.376, 0.49412, 0.32372, 0.70762, 0.42829, 0.82407, 0.59676, 0.89061, 0.75943, 0.86289, 0.68972, 0.65216, 0.72457, 0.58562, 0.34695, 0.19189, 0.76524, 0.18634, 0.60286, 0.83097, 0.72852, 0.7772, 0.92352, 0.81029, 0.76977, 0.41023, 0.34996, 0.34114, 0.55029, 0.78341, 0.85029, 0.76627], "triangles": [27, 21, 22, 27, 9, 21, 27, 22, 26, 8, 27, 14, 9, 10, 11, 22, 12, 13, 21, 11, 12, 9, 11, 21, 21, 12, 22, 22, 13, 0, 26, 22, 0, 20, 14, 26, 19, 14, 20, 15, 8, 14, 28, 15, 14, 26, 0, 1, 20, 26, 1, 29, 19, 20, 7, 8, 15, 1, 29, 20, 24, 19, 29, 19, 28, 14, 28, 19, 24, 25, 29, 1, 16, 15, 28, 23, 28, 24, 18, 24, 29, 23, 24, 18, 25, 1, 2, 17, 28, 23, 17, 23, 18, 4, 25, 2, 25, 18, 29, 3, 4, 2, 4, 18, 25, 17, 6, 16, 17, 16, 28, 7, 15, 16, 6, 7, 16, 5, 17, 18, 5, 18, 4, 6, 17, 5, 27, 8, 9, 14, 27, 26], "vertices": [3, 4, -1.74, -53.71, 0.47242, 5, -1.74, 31.29, 0.42528, 109, -1.74, 36.29, 0.1023, 3, 4, -1.74, -81.72, 0.458, 5, -1.74, 3.28, 0.32108, 109, -1.74, 8.28, 0.22092, 3, 4, -1.74, -88.35, 0.45193, 5, -1.74, -3.35, 0.30223, 109, -1.74, 1.65, 0.24585, 3, 4, -1.74, -92.44, 0.43803, 5, -1.74, -7.44, 0.29951, 109, -1.74, -2.44, 0.26246, 3, 4, 1.36, -92, 0.44051, 5, 1.36, -7, 0.30225, 109, 1.36, -2, 0.25723, 3, 4, 4.29, -93.37, 0.42359, 5, 4.29, -8.37, 0.29487, 109, 4.29, -3.37, 0.28154, 3, 4, 8.56, -93.81, 0.43044, 5, 8.56, -8.81, 0.26802, 109, 8.56, -3.81, 0.30154, 3, 4, 17.1, -82.7, 0.426, 5, 17.1, 2.3, 0.32662, 109, 17.1, 7.3, 0.24738, 3, 4, 16.49, -69.05, 0.414, 5, 16.49, 15.95, 0.33892, 109, 16.49, 20.95, 0.24708, 2, 4, 19.26, -52.75, 0.5, 5, 19.26, 32.25, 0.5, 2, 4, 19.26, -43.52, 0.5, 5, 19.26, 41.48, 0.5, 2, 4, 11.98, -42.54, 0.5, 5, 11.98, 42.46, 0.5, 2, 4, 4.05, -43.86, 0.5, 5, 4.05, 41.14, 0.5, 2, 4, -1.74, -43.64, 0.508, 5, -1.74, 41.36, 0.492, 3, 4, 11.37, -68.46, 0.43516, 5, 11.37, 16.54, 0.37727, 109, 11.37, 21.54, 0.18757, 3, 4, 12.47, -80.06, 0.43, 5, 12.47, 4.94, 0.35369, 109, 12.47, 9.94, 0.21631, 3, 4, 10.27, -86.07, 0.43761, 5, 10.27, -1.07, 0.32116, 109, 10.27, 3.93, 0.24123, 3, 4, 6.73, -89.44, 0.43906, 5, 6.73, -4.44, 0.3034, 109, 6.73, 0.56, 0.25754, 3, 4, 3.32, -87.33, 0.43263, 5, 3.32, -2.33, 0.32144, 109, 3.32, 2.67, 0.24593, 3, 4, 4.78, -78.06, 0.47242, 5, 4.78, 6.94, 0.35927, 109, 4.78, 11.94, 0.16831, 3, 4, 4.05, -73.81, 0.40887, 5, 4.05, 11.19, 0.34649, 109, 4.05, 16.19, 0.24464, 3, 4, 11.98, -52.96, 0.444, 5, 11.98, 32.04, 0.432, 109, 11.98, 37.04, 0.124, 3, 4, 3.19, -51.84, 0.432, 5, 3.19, 33.16, 0.432, 109, 3.19, 38.16, 0.136, 3, 4, 6.6, -85.49, 0.43605, 5, 6.6, -0.49, 0.32734, 109, 6.6, 4.51, 0.23662, 3, 4, 3.96, -83.14, 0.45595, 5, 3.96, 1.86, 0.33943, 109, 3.96, 6.86, 0.20461, 3, 4, -0.13, -84.95, 0.45904, 5, -0.13, 0.05, 0.33204, 109, -0.13, 5.05, 0.20892, 3, 4, 3.1, -64.55, 0.44387, 5, 3.1, 20.45, 0.40922, 109, 3.1, 25.45, 0.14691, 3, 4, 11.91, -60.19, 0.384, 5, 11.91, 24.81, 0.35926, 109, 11.91, 29.81, 0.25674, 3, 4, 7.71, -83.88, 0.44392, 5, 7.71, 1.12, 0.32315, 109, 7.71, 6.12, 0.23292, 3, 4, 1.41, -82.73, 0.446, 5, 1.41, 2.27, 0.33338, 109, 1.41, 7.27, 0.22061], "hull": 14, "edges": [18, 20, 18, 16, 16, 14, 14, 12, 20, 22, 10, 12, 10, 8, 4, 6, 8, 6, 38, 40, 4, 2, 40, 2, 38, 28, 28, 30, 22, 24, 24, 26, 2, 0, 0, 26], "width": 17, "height": 36}}, "Nose": {"Nose": {"type": "mesh", "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "triangles": [11, 7, 18, 20, 19, 0, 19, 18, 10, 19, 10, 0, 17, 11, 19, 11, 13, 7, 13, 21, 7, 14, 11, 17, 14, 13, 11, 22, 13, 14, 22, 21, 13, 12, 16, 20, 20, 16, 19, 16, 17, 19, 15, 17, 16, 15, 14, 17, 5, 23, 4, 4, 23, 15, 4, 16, 12, 4, 15, 16, 1, 2, 3, 3, 20, 1, 0, 1, 20, 4, 12, 3, 12, 20, 3, 5, 22, 23, 5, 6, 22, 23, 14, 15, 23, 22, 14, 22, 6, 21, 21, 6, 7, 11, 18, 19, 10, 18, 9, 7, 8, 18, 18, 8, 9], "vertices": [2, 5, 0.83, 54.01, 0.436, 4, 0.83, -30.99, 0.564, 2, 5, 0.83, 40.2, 0.428, 4, 0.83, -44.8, 0.572, 2, 5, -1.21, 40.2, 0.42, 4, -1.21, -44.8, 0.58, 2, 5, -2.92, 42, 0.428, 4, -2.92, -43, 0.572, 2, 5, -10.03, 41.01, 0.436, 4, -10.03, -43.99, 0.564, 2, 5, -21.17, 44.61, 0.436, 4, -21.17, -40.39, 0.564, 2, 5, -21.17, 51.09, 0.436, 4, -21.17, -33.91, 0.564, 2, 5, -14.3, 53.02, 0.436, 4, -14.3, -31.98, 0.564, 2, 5, -12.41, 58.2, 0.436, 4, -12.41, -26.8, 0.564, 2, 5, -9.26, 58.2, 0.436, 4, -9.26, -26.8, 0.564, 2, 5, -3.68, 58.2, 0.436, 4, -3.68, -26.8, 0.564, 2, 5, -10.57, 49.87, 0.42, 4, -10.57, -35.13, 0.58, 2, 5, -4.72, 43.89, 0.42, 4, -4.72, -41.11, 0.58, 2, 5, -14.57, 49.15, 0.412, 4, -14.57, -35.85, 0.588, 2, 5, -13.9, 47.17, 0.412, 4, -13.9, -37.83, 0.588, 2, 5, -10.25, 45.69, 0.42, 4, -10.25, -39.31, 0.58, 2, 5, -6.7, 45.51, 0.412, 4, -6.7, -39.49, 0.588, 2, 5, -8.9, 47.85, 0.412, 4, -8.9, -37.15, 0.588, 2, 5, -10.48, 53.61, 0.42, 4, -10.48, -31.39, 0.58, 2, 5, -7.15, 49.56, 0.42, 4, -7.15, -35.44, 0.58, 2, 5, -3.37, 46.14, 0.412, 4, -3.37, -38.86, 0.588, 2, 5, -17.41, 50.5, 0.428, 4, -17.41, -34.5, 0.572, 2, 5, -17.95, 48.16, 0.428, 4, -17.95, -36.84, 0.572, 2, 5, -15.61, 46, 0.42, 4, -15.61, -39, 0.58], "hull": 11, "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20], "width": 23, "height": 18}}, "Nose2": {"Nose": {"type": "mesh", "uvs": [1, 0.2328, 1, 1, 0.90724, 1, 0.82951, 0.90029, 0.50634, 0.95529, 0, 0.75529, 0, 0.3953, 0.31202, 0.2878, 0.39793, 0, 0.54111, 0, 0.79474, 0, 0.48179, 0.4628, 0.7477, 0.79529, 0.29975, 0.5028, 0.33043, 0.61279, 0.49611, 0.69529, 0.6577, 0.70529, 0.55747, 0.5753, 0.48588, 0.2553, 0.63724, 0.4803, 0.80906, 0.67029, 0.17089, 0.4278, 0.14634, 0.5578, 0.25271, 0.67779], "triangles": [18, 8, 9, 7, 8, 18, 21, 6, 7, 11, 7, 18, 13, 21, 7, 10, 18, 9, 19, 10, 0, 19, 18, 10, 11, 18, 19, 11, 13, 7, 22, 6, 21, 22, 21, 13, 17, 11, 19, 14, 13, 11, 14, 11, 17, 22, 13, 14, 20, 19, 0, 16, 17, 19, 23, 22, 14, 15, 14, 17, 20, 16, 19, 15, 17, 16, 5, 6, 22, 5, 22, 23, 12, 16, 20, 1, 2, 3, 12, 20, 3, 4, 15, 16, 4, 16, 12, 23, 14, 15, 4, 23, 15, 5, 23, 4, 4, 12, 3, 3, 20, 1, 0, 1, 20], "vertices": [2, 4, -1.46, -30.99, 0.572, 5, -1.46, 54.01, 0.428, 2, 4, -1.46, -44.8, 0.572, 5, -1.46, 40.2, 0.428, 2, 4, 0.58, -44.8, 0.572, 5, 0.58, 40.2, 0.428, 2, 4, 2.29, -43, 0.58, 5, 2.29, 42, 0.42, 2, 4, 9.4, -43.99, 0.572, 5, 9.4, 41.01, 0.428, 2, 4, 20.54, -40.39, 0.572, 5, 20.54, 44.61, 0.428, 2, 4, 20.54, -33.91, 0.572, 5, 20.54, 51.09, 0.428, 2, 4, 13.68, -31.98, 0.588, 5, 13.68, 53.02, 0.412, 2, 4, 11.79, -26.8, 0.572, 5, 11.79, 58.2, 0.428, 2, 4, 8.64, -26.8, 0.572, 5, 8.64, 58.2, 0.428, 2, 4, 3.06, -26.8, 0.572, 5, 3.06, 58.2, 0.428, 2, 4, 9.94, -35.13, 0.604, 5, 9.94, 49.87, 0.396, 2, 4, 4.09, -41.11, 0.588, 5, 4.09, 43.89, 0.412, 2, 4, 13.95, -35.85, 0.588, 5, 13.95, 49.15, 0.412, 2, 4, 13.27, -37.83, 0.596, 5, 13.27, 47.17, 0.404, 2, 4, 9.63, -39.31, 0.596, 5, 9.63, 45.69, 0.404, 2, 4, 6.07, -39.49, 0.604, 5, 6.07, 45.51, 0.396, 2, 4, 8.28, -37.15, 0.604, 5, 8.28, 47.85, 0.396, 2, 4, 9.85, -31.39, 0.588, 5, 9.85, 53.61, 0.412, 2, 4, 6.52, -35.44, 0.588, 5, 6.52, 49.56, 0.412, 2, 4, 2.74, -38.86, 0.572, 5, 2.74, 46.14, 0.428, 2, 4, 16.78, -34.5, 0.58, 5, 16.78, 50.5, 0.42, 2, 4, 17.32, -36.84, 0.58, 5, 17.32, 48.16, 0.42, 2, 4, 14.98, -39, 0.596, 5, 14.98, 46, 0.404], "hull": 11, "edges": [12, 14, 14, 16, 2, 0, 20, 0, 10, 12, 10, 8, 8, 6, 2, 4, 6, 4, 16, 18, 18, 20], "width": 23, "height": 18}}, "Rubi": {"Rubi": {"x": -0.07, "y": 0.33, "width": 29, "height": 29}}, "Sparkle": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle2": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle3": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle4": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle5": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle6": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle7": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "Sparkle8": {"Sparkle": {"x": -0.16, "y": -0.21, "width": 36, "height": 39}}, "bg_effect_1": {"bg_effect_1": {"x": -33.33, "y": -0.78, "scaleX": 1.2, "scaleY": 1.2, "width": 54, "height": 100}}, "bg_effect_2": {"bg_effect_1": {"x": 33.33, "y": -0.78, "scaleX": -1.2, "scaleY": 1.2, "width": 54, "height": 100}}, "frame_set/E0": {"frame_set/E0": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E4": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}}, "frame_set/E1": {"frame_set/E0": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E4": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}}, "frame_set/E2": {"frame_set/E0": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E4": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}}, "frame_set/E3": {"frame_set/E0": {"width": 56, "height": 48}, "frame_set/E2": {"width": 56, "height": 48}, "frame_set/E4": {"width": 56, "height": 48}, "frame_set/E6": {"width": 56, "height": 48}, "frame_set/E8": {"width": 56, "height": 48}}, "set3/El0": {"set3/El00": {"width": 88, "height": 88}, "set3/El01": {"width": 88, "height": 88}, "set3/El02": {"width": 88, "height": 88}, "set3/El03": {"width": 88, "height": 88}, "set3/El04": {"width": 88, "height": 88}, "set3/El05": {"width": 88, "height": 88}, "set3/El06": {"width": 88, "height": 88}, "set3/El07": {"width": 88, "height": 88}, "set3/El08": {"width": 88, "height": 88}}, "set3/El00": {"set3/El00": {"width": 88, "height": 88}, "set3/El01": {"width": 88, "height": 88}, "set3/El02": {"width": 88, "height": 88}, "set3/El03": {"width": 88, "height": 88}, "set3/El04": {"width": 88, "height": 88}, "set3/El05": {"width": 88, "height": 88}, "set3/El06": {"width": 88, "height": 88}, "set3/El07": {"width": 88, "height": 88}, "set3/El08": {"width": 88, "height": 88}}, "set3/El1": {"set3/El00": {"width": 88, "height": 88}, "set3/El01": {"width": 88, "height": 88}, "set3/El02": {"width": 88, "height": 88}, "set3/El03": {"width": 88, "height": 88}, "set3/El04": {"width": 88, "height": 88}, "set3/El05": {"width": 88, "height": 88}, "set3/El06": {"width": 88, "height": 88}, "set3/El07": {"width": 88, "height": 88}, "set3/El08": {"width": 88, "height": 88}}}}, "animations": {"with_frame": {"slots": {"Sparkle": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.3667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0.6, "name": "Sparkle"}]}, "Sparkle4": {"color": [{"time": 0.7333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "Sparkle"}]}, "Sparkle5": {"color": [{"time": 0.9667, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 0.9667, "name": "Sparkle"}]}, "Sparkle6": {"color": [{"time": 0.2667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": "Sparkle"}]}, "Sparkle7": {"color": [{"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "Sparkle"}]}, "Sparkle8": {"color": [{"time": 0.7667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "Sparkle"}]}, "bg_effect_1": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "color": "fffffffd", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff17"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff59"}, {"time": 1.4, "color": "ffffff00"}]}, "bg_effect_2": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "color": "fffffffd", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffff17"}, {"time": 0.9, "color": "ffffffff"}, {"time": 1.0667, "color": "ffffff59"}, {"time": 1.4, "color": "ffffff00"}]}, "frame_set/E0": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "frame_set/E0"}, {"time": 0.1, "name": null}, {"time": 0.2, "name": "frame_set/E2"}, {"time": 0.2333, "name": null}, {"time": 0.3333, "name": "frame_set/E4"}, {"time": 0.3667, "name": null}, {"time": 0.4667, "name": "frame_set/E6"}, {"time": 0.5, "name": null}, {"time": 0.6, "name": "frame_set/E8"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "frame_set/E0"}, {"time": 0.7, "name": null}, {"time": 0.8, "name": "frame_set/E2"}, {"time": 0.8333, "name": null}, {"time": 0.9333, "name": "frame_set/E4"}, {"time": 0.9667, "name": null}, {"time": 1.0667, "name": "frame_set/E6"}, {"time": 1.1, "name": null}, {"time": 1.2, "name": "frame_set/E8"}, {"time": 1.2333, "name": null}, {"time": 1.2667, "name": "frame_set/E0"}, {"time": 1.3, "name": null}, {"time": 1.4, "name": "frame_set/E2"}, {"time": 1.4333, "name": null}, {"time": 1.5333, "name": "frame_set/E4"}, {"time": 1.5667, "name": null}, {"time": 1.6667, "name": "frame_set/E6"}, {"time": 1.7, "name": null}, {"time": 1.8, "name": "frame_set/E8"}, {"time": 1.8333, "name": null}]}, "frame_set/E1": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E0"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E2"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E4"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E6"}, {"time": 0.4667, "name": null}, {"time": 0.5667, "name": "frame_set/E8"}, {"time": 0.6, "name": null}, {"time": 0.6333, "name": "frame_set/E0"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E2"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E4"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E6"}, {"time": 1.0667, "name": null}, {"time": 1.1667, "name": "frame_set/E8"}, {"time": 1.2, "name": null}, {"time": 1.2333, "name": "frame_set/E0"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E2"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E4"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E6"}, {"time": 1.6667, "name": null}, {"time": 1.7667, "name": "frame_set/E8"}, {"time": 1.8, "name": null}]}, "frame_set/E2": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E2"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E4"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E6"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E8"}, {"time": 0.4667, "name": null}, {"time": 0.5, "name": "frame_set/E0"}, {"time": 0.5333, "name": null}, {"time": 0.6333, "name": "frame_set/E2"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E4"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E6"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E8"}, {"time": 1.0667, "name": null}, {"time": 1.1, "name": "frame_set/E0"}, {"time": 1.1333, "name": null}, {"time": 1.2333, "name": "frame_set/E2"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E4"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E6"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E8"}, {"time": 1.6667, "name": null}, {"time": 1.7, "name": "frame_set/E0"}, {"time": 1.7333, "name": null}]}, "frame_set/E3": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E0"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E2"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E4"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E6"}, {"time": 0.4667, "name": null}, {"time": 0.5667, "name": "frame_set/E8"}, {"time": 0.6, "name": null}, {"time": 0.6333, "name": "frame_set/E0"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E2"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E4"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E6"}, {"time": 1.0667, "name": null}, {"time": 1.1667, "name": "frame_set/E8"}, {"time": 1.2, "name": null}, {"time": 1.2333, "name": "frame_set/E0"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E2"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E4"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E6"}, {"time": 1.6667, "name": null}, {"time": 1.7667, "name": "frame_set/E8"}, {"time": 1.8, "name": null}]}, "set3/El00": {"attachment": [{"time": 0.3333, "name": "set3/El00"}, {"time": 0.3667, "name": null}, {"time": 0.4, "name": "set3/El01"}, {"time": 0.4333, "name": null}, {"time": 0.4667, "name": "set3/El02"}, {"time": 0.5, "name": null}, {"time": 0.5333, "name": "set3/El03"}, {"time": 0.5667, "name": null}, {"time": 0.6, "name": "set3/El04"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El05"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El06"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El07"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El08"}, {"time": 0.9, "name": null}]}, "set3/El0": {"attachment": [{"time": 0.4667, "name": "set3/El00"}, {"time": 0.5, "name": null}, {"time": 0.5333, "name": "set3/El01"}, {"time": 0.5667, "name": null}, {"time": 0.6, "name": "set3/El02"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El03"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El04"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El05"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El06"}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "set3/El07"}, {"time": 0.9667, "name": null}, {"time": 1, "name": "set3/El08"}, {"time": 1.0333, "name": null}]}, "set3/El1": {"attachment": [{"time": 0.6, "name": "set3/El00"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El01"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El02"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El03"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El04"}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "set3/El05"}, {"time": 0.9667, "name": null}, {"time": 1, "name": "set3/El06"}, {"time": 1.0333, "name": null}, {"time": 1.0667, "name": "set3/El07"}, {"time": 1.1, "name": null}, {"time": 1.1333, "name": "set3/El08"}, {"time": 1.1667, "name": null}]}}, "bones": {"Dragon": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "angle": 11.56, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "angle": -9.39, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "angle": 5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 2.83, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": -19.02, "y": 0.65, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 10.84, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 0.908, "y": 0.908, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": 1.1, "y": 1.1, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "3D": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 0, "y": 108.4, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": -216.88, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 264.84, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Mouth_B": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 39.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 5.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -7.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Rubi": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -88.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -7.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -1.59, "y": -3.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.12, "y": -0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1}]}, "Fang2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 3.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 8.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "scale": [{"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Fang": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 14.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -6.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 3.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -3.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "scale": [{"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Eyebrow": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -7.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 8.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 9.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 8.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 0.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -5.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -10.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -9.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -0.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -9.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -3.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -13.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard71": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 9.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 6.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -2.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 3.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -8.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eye2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 14.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 18.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": -1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -2.75, "y": -1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.85, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.85, "y": 0.709, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Eye": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -18.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.54, "y": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.91, "y": -2.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.85, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.85, "y": 0.637, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Beard3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 2.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -6.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 24.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 11.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 14.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -5.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 11.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -4.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard72": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -5.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -7.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -2.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 3.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard73": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -19.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -21.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -25.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -2.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -15.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 2.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -6.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -8.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -4.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -13.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -13.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -0.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -10.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -6.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 9.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 4.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -1.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 5.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 5.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 0.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 12.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 7.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -7.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 6.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -8.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 17.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 23.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 23.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 11.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -6.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -13.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -11.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -22.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 19.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -12.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 13.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -22.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 4.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -17.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -20.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 12.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -6.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 14.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth15": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -20.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 12.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -6.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 14.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -6.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Tong_MouthL": {"scale": [{"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Bread_Mouth10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -15.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -15.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 5.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 9.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -4.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 13.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 15.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -4.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 13.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -16.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 6.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -9.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 10.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 4.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -25.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -6.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -21.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -23.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -13.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 16.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 12.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -2.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 23.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 17.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard40": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -9.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -12.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -6.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -13.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -6.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard16": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -15.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -13.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -3.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -17.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard41": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 8.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -3.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 5.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 6.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 1.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 9.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard42": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 8.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -7.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -8.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard38": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 8.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -0.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 6.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 6.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard39": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 10.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -11.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -6.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 9.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard46": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 8.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 8.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 2.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard43": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -1.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 8.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 2.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard44": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -3.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 2.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 10.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 4.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard45": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -12.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 2.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -14.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -14.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -12.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard47": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 3.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -1.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard48": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -10.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 2.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -9.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 2.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -8.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard35": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -10.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 3.71, "y": -0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Beard31": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard28": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard24": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard20": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard17": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard36": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 1.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -9.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -5.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -10.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -6.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -12.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard37": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 14.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -3.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 7.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -5.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard32": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 1.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -6.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -11.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -10.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard34": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 19.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 15.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -13.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -9.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard33": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 13.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -2.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 2.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 15.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard29": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 4.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 2.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 2.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -3.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard30": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 18.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 19.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 23.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard25": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 2.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 1.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -4.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard27": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 20.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -0.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 19.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -3.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 15.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard26": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 9.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 7.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -5.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard21": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 8.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 10.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 1.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 8.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard23": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 17.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -1.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 17.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -1.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 19.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard22": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 11.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -6.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 5.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -7.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard18": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -1.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 8.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 9.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard19": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 31.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 4.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 27.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 9.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard68": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard64": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard61": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard57": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard50": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard53": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard51": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -3.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 8.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 9.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 14.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard52": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -9.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 7.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -9.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -5.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 6.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard54": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 3.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -2.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 2.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": -2.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard55": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 10.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 11.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 2.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 13.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": 4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard56": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -39.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 5.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -9.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": 6.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard58": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -3.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -0.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -6.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard60": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 9.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard59": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 8.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 7.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 6.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard62": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -11.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -7.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -6.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 2.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard63": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -18.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -19.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -21.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -6.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -21.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard65": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -11.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -7.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -5.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -10.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard67": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -7.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -7.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -15.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard66": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -1.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -7.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -0.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -5.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 1.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard69": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -12.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard70": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -12.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -11.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 6.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -26.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -18.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -14.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 12.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -0.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -16.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -20.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -12.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -11.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 4.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -12.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -11.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 4.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -8.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 7.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -19.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 11.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -22.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 10.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -24.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -7.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -13.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard74": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard75": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard76": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard77": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard78": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard79": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 19.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -16.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 21.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 11.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard80": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard81": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": 8.88, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 14.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 20.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 26.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard82": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -4.79, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard83": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -3.14, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard84": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -10.76, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Fx_BG": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 2.05, "y": 2.05}]}, "Fx_BG2": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 2.05, "y": 2.05}]}, "Fx_BG3": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 2.05, "y": 2.05}]}, "Fx_Rubi2": {"rotate": [{"time": 0, "angle": 146.76}], "translate": [{"time": 0, "x": -2.4, "y": -0.66}], "scale": [{"time": 0, "x": 0.48, "y": 0.349}]}, "Fx_Rubi3": {"rotate": [{"time": 0, "angle": 19.44}], "translate": [{"time": 0, "x": 1.42, "y": -0.47}], "scale": [{"time": 0, "x": 0.48, "y": 0.349}]}, "Fx_Rubi4": {"rotate": [{"time": 0, "angle": -15.1}]}, "Sparkle": {"rotate": [{"time": 0.5, "angle": 0}, {"time": 1.1, "angle": 111.55}], "scale": [{"time": 0.5, "x": 1, "y": 1}, {"time": 0.8, "x": 1.208, "y": 1.208}, {"time": 1.1, "x": 0.63, "y": 0.63}]}, "Sparkle2": {"rotate": [{"time": 0.3667, "angle": 0}, {"time": 0.9667, "angle": 111.55}], "scale": [{"time": 0.3667, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.208, "y": 1.208}, {"time": 0.9667, "x": 0.63, "y": 0.63}]}, "Sparkle3": {"rotate": [{"time": 0.6, "angle": 0}, {"time": 1.2, "angle": 111.55}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.9, "x": 1.208, "y": 1.208}, {"time": 1.2, "x": 0.63, "y": 0.63}]}, "Sparkle4": {"rotate": [{"time": 0.7333, "angle": 0}, {"time": 1.3333, "angle": 111.55}], "scale": [{"time": 0.7333, "x": 1, "y": 1}, {"time": 1.0333, "x": 1.208, "y": 1.208}, {"time": 1.3333, "x": 0.63, "y": 0.63}]}, "Sparkle5": {"rotate": [{"time": 0.9667, "angle": 0}, {"time": 1.5667, "angle": 111.55}], "scale": [{"time": 0.9667, "x": 1, "y": 1}, {"time": 1.2667, "x": 1.208, "y": 1.208}, {"time": 1.5667, "x": 0.63, "y": 0.63}]}, "Sparkle6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.8667, "angle": 111.55, "curve": "stepped"}, {"time": 1.2, "angle": -36.25}, {"time": 1.8, "angle": 111.55}], "translate": [{"time": 1.2, "x": -5.92, "y": 11.25}], "scale": [{"time": 0.2667, "x": 1, "y": 1}, {"time": 0.5667, "x": 1.208, "y": 1.208}, {"time": 0.8667, "x": 0.63, "y": 0.63, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1}, {"time": 1.5, "x": 1.208, "y": 1.208}, {"time": 1.8, "x": 0.63, "y": 0.63}]}, "Sparkle7": {"rotate": [{"time": 0.8333, "angle": 0}, {"time": 1.4333, "angle": 111.55}], "scale": [{"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.208, "y": 1.208}, {"time": 1.4333, "x": 0.63, "y": 0.63}]}, "Sparkle8": {"rotate": [{"time": 0.7667, "angle": 0}, {"time": 1.3667, "angle": 111.55}], "translate": [{"time": 0.7667, "x": -11.04, "y": 0.94}], "scale": [{"time": 0.7667, "x": 1, "y": 1}, {"time": 1.0667, "x": 1.208, "y": 1.208}, {"time": 1.3667, "x": 0.63, "y": 0.63}]}}}, "without_frame": {"slots": {"BG": {"attachment": [{"time": 0, "name": null}]}, "BG2": {"attachment": [{"time": 0, "name": null}]}, "Frame": {"attachment": [{"time": 0, "name": null}]}, "Frame2": {"attachment": [{"time": 0, "name": null}]}, "Frame_Bottom": {"attachment": [{"time": 0, "name": null}]}, "Frame_Bottom2": {"attachment": [{"time": 0, "name": null}]}, "Frame_Top": {"attachment": [{"time": 0, "name": null}]}, "Frame_Top2": {"attachment": [{"time": 0, "name": null}]}, "Sparkle": {"color": [{"time": 0.5, "color": "ffffff00"}, {"time": 0.6667, "color": "ffffffff", "curve": "stepped"}, {"time": 0.9333, "color": "ffffffff"}, {"time": 1.1, "color": "ffffff00"}], "attachment": [{"time": 0.5, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.3667, "color": "ffffff00"}, {"time": 0.5333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.8, "color": "ffffffff"}, {"time": 0.9667, "color": "ffffff00"}], "attachment": [{"time": 0.3667, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 0.6, "color": "ffffff00"}, {"time": 0.7667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.0333, "color": "ffffffff"}, {"time": 1.2, "color": "ffffff00"}], "attachment": [{"time": 0.6, "name": "Sparkle"}]}, "Sparkle4": {"color": [{"time": 0.7333, "color": "ffffff00"}, {"time": 0.9, "color": "ffffffff", "curve": "stepped"}, {"time": 1.1667, "color": "ffffffff"}, {"time": 1.3333, "color": "ffffff00"}], "attachment": [{"time": 0.7333, "name": "Sparkle"}]}, "Sparkle5": {"color": [{"time": 0.9667, "color": "ffffff00"}, {"time": 1.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.4, "color": "ffffffff"}, {"time": 1.5667, "color": "ffffff00"}], "attachment": [{"time": 0.9667, "name": "Sparkle"}]}, "Sparkle6": {"color": [{"time": 0.2667, "color": "ffffff00"}, {"time": 0.4333, "color": "ffffffff", "curve": "stepped"}, {"time": 0.7, "color": "ffffffff"}, {"time": 0.8667, "color": "ffffff00", "curve": "stepped"}, {"time": 1.2, "color": "ffffff00"}, {"time": 1.3667, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff"}, {"time": 1.8, "color": "ffffff00"}], "attachment": [{"time": 0.2667, "name": "Sparkle"}]}, "Sparkle7": {"color": [{"time": 0.8333, "color": "ffffff00"}, {"time": 1, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2667, "color": "ffffffff"}, {"time": 1.4333, "color": "ffffff00"}], "attachment": [{"time": 0.8333, "name": "Sparkle"}]}, "Sparkle8": {"color": [{"time": 0.7667, "color": "ffffff00"}, {"time": 0.9333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.2, "color": "ffffffff"}, {"time": 1.3667, "color": "ffffff00"}], "attachment": [{"time": 0.7667, "name": "Sparkle"}]}, "frame_set/E0": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "frame_set/E0"}, {"time": 0.1, "name": null}, {"time": 0.2, "name": "frame_set/E2"}, {"time": 0.2333, "name": null}, {"time": 0.3333, "name": "frame_set/E4"}, {"time": 0.3667, "name": null}, {"time": 0.4667, "name": "frame_set/E6"}, {"time": 0.5, "name": null}, {"time": 0.6, "name": "frame_set/E8"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "frame_set/E0"}, {"time": 0.7, "name": null}, {"time": 0.8, "name": "frame_set/E2"}, {"time": 0.8333, "name": null}, {"time": 0.9333, "name": "frame_set/E4"}, {"time": 0.9667, "name": null}, {"time": 1.0667, "name": "frame_set/E6"}, {"time": 1.1, "name": null}, {"time": 1.2, "name": "frame_set/E8"}, {"time": 1.2333, "name": null}, {"time": 1.2667, "name": "frame_set/E0"}, {"time": 1.3, "name": null}, {"time": 1.4, "name": "frame_set/E2"}, {"time": 1.4333, "name": null}, {"time": 1.5333, "name": "frame_set/E4"}, {"time": 1.5667, "name": null}, {"time": 1.6667, "name": "frame_set/E6"}, {"time": 1.7, "name": null}, {"time": 1.8, "name": "frame_set/E8"}, {"time": 1.8333, "name": null}]}, "frame_set/E1": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E0"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E2"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E4"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E6"}, {"time": 0.4667, "name": null}, {"time": 0.5667, "name": "frame_set/E8"}, {"time": 0.6, "name": null}, {"time": 0.6333, "name": "frame_set/E0"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E2"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E4"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E6"}, {"time": 1.0667, "name": null}, {"time": 1.1667, "name": "frame_set/E8"}, {"time": 1.2, "name": null}, {"time": 1.2333, "name": "frame_set/E0"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E2"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E4"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E6"}, {"time": 1.6667, "name": null}, {"time": 1.7667, "name": "frame_set/E8"}, {"time": 1.8, "name": null}]}, "frame_set/E2": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E2"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E4"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E6"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E8"}, {"time": 0.4667, "name": null}, {"time": 0.5, "name": "frame_set/E0"}, {"time": 0.5333, "name": null}, {"time": 0.6333, "name": "frame_set/E2"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E4"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E6"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E8"}, {"time": 1.0667, "name": null}, {"time": 1.1, "name": "frame_set/E0"}, {"time": 1.1333, "name": null}, {"time": 1.2333, "name": "frame_set/E2"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E4"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E6"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E8"}, {"time": 1.6667, "name": null}, {"time": 1.7, "name": "frame_set/E0"}, {"time": 1.7333, "name": null}]}, "frame_set/E3": {"color": [{"time": 0, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "color": "ffffffff", "curve": "stepped"}, {"time": 1.6333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "color": "ffffff00"}], "attachment": [{"time": 0.0333, "name": "frame_set/E0"}, {"time": 0.0667, "name": null}, {"time": 0.1667, "name": "frame_set/E2"}, {"time": 0.2, "name": null}, {"time": 0.3, "name": "frame_set/E4"}, {"time": 0.3333, "name": null}, {"time": 0.4333, "name": "frame_set/E6"}, {"time": 0.4667, "name": null}, {"time": 0.5667, "name": "frame_set/E8"}, {"time": 0.6, "name": null}, {"time": 0.6333, "name": "frame_set/E0"}, {"time": 0.6667, "name": null}, {"time": 0.7667, "name": "frame_set/E2"}, {"time": 0.8, "name": null}, {"time": 0.9, "name": "frame_set/E4"}, {"time": 0.9333, "name": null}, {"time": 1.0333, "name": "frame_set/E6"}, {"time": 1.0667, "name": null}, {"time": 1.1667, "name": "frame_set/E8"}, {"time": 1.2, "name": null}, {"time": 1.2333, "name": "frame_set/E0"}, {"time": 1.2667, "name": null}, {"time": 1.3667, "name": "frame_set/E2"}, {"time": 1.4, "name": null}, {"time": 1.5, "name": "frame_set/E4"}, {"time": 1.5333, "name": null}, {"time": 1.6333, "name": "frame_set/E6"}, {"time": 1.6667, "name": null}, {"time": 1.7667, "name": "frame_set/E8"}, {"time": 1.8, "name": null}]}, "set3/El00": {"attachment": [{"time": 0.3333, "name": "set3/El00"}, {"time": 0.3667, "name": null}, {"time": 0.4, "name": "set3/El01"}, {"time": 0.4333, "name": null}, {"time": 0.4667, "name": "set3/El02"}, {"time": 0.5, "name": null}, {"time": 0.5333, "name": "set3/El03"}, {"time": 0.5667, "name": null}, {"time": 0.6, "name": "set3/El04"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El05"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El06"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El07"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El08"}, {"time": 0.9, "name": null}]}, "set3/El0": {"attachment": [{"time": 0.4667, "name": "set3/El00"}, {"time": 0.5, "name": null}, {"time": 0.5333, "name": "set3/El01"}, {"time": 0.5667, "name": null}, {"time": 0.6, "name": "set3/El02"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El03"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El04"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El05"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El06"}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "set3/El07"}, {"time": 0.9667, "name": null}, {"time": 1, "name": "set3/El08"}, {"time": 1.0333, "name": null}]}, "set3/El1": {"attachment": [{"time": 0.6, "name": "set3/El00"}, {"time": 0.6333, "name": null}, {"time": 0.6667, "name": "set3/El01"}, {"time": 0.7, "name": null}, {"time": 0.7333, "name": "set3/El02"}, {"time": 0.7667, "name": null}, {"time": 0.8, "name": "set3/El03"}, {"time": 0.8333, "name": null}, {"time": 0.8667, "name": "set3/El04"}, {"time": 0.9, "name": null}, {"time": 0.9333, "name": "set3/El05"}, {"time": 0.9667, "name": null}, {"time": 1, "name": "set3/El06"}, {"time": 1.0333, "name": null}, {"time": 1.0667, "name": "set3/El07"}, {"time": 1.1, "name": null}, {"time": 1.1333, "name": "set3/El08"}, {"time": 1.1667, "name": null}]}}, "bones": {"Dragon": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "angle": 11.56, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "angle": -9.39, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "angle": 5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 2.83, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": -19.02, "y": 0.65, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 10.84, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 0.908, "y": 0.908, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": 1.1, "y": 1.1, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "3D": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 1, 0.8]}, {"time": 0.3333, "x": 0, "y": 108.4, "curve": [0, 0.13, 0.75, 1]}, {"time": 0.6667, "x": -216.88, "y": 0, "curve": [0, 0.13, 0.75, 1]}, {"time": 1.5, "x": 264.84, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Mouth_B": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 39.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": 0, "y": 0.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 0, "y": 5.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 0, "y": -3.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "x": 0, "y": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0, "y": -7.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Rubi": {"rotate": [{"time": 0.1667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -88.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -7.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0, "y": 5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "x": -1.59, "y": -3.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.12, "y": -0.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.902, "y": 0.902, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1, "y": 1}]}, "Fang2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 3.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 8.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "scale": [{"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Fang": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 14.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -6.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 3.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -3.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "scale": [{"time": 0.5, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Eyebrow": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -7.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 8.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 9.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 8.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 0.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -5.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -10.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -9.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -0.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -9.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -3.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -13.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard71": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 9.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -5.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 6.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -2.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 3.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -8.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 0.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eye2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 14.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 18.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": -1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -2.75, "y": -1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.85, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.85, "y": 0.709, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Eye": {"rotate": [{"time": 0.3333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -18.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.54, "y": -1.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -0.91, "y": -2.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.2, "y": 1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0.85, "y": 0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 0.85, "y": 0.637, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 1, "y": 1}]}, "Beard3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 2.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 0.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -6.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 24.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 11.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 14.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -5.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 11.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -4.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard72": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -5.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -7.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -2.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 3.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard73": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -19.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -21.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -25.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -2.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -15.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 2.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -6.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -8.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -4.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -13.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -13.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -0.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -10.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -6.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 9.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 4.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -1.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 5.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -1.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 5.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 0.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Eyebrow3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 12.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 7.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -7.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 6.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -8.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 17.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 23.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 11.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -10.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 14.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 23.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 9.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 11.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -6.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -13.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 0.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -11.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -22.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 19.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -12.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 13.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -22.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 4.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -17.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -20.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 12.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -6.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 14.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth15": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 9.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -20.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 12.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -6.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 14.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -6.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Tong_MouthL": {"scale": [{"time": 0.3333, "x": 0.992, "y": 0.992}]}, "Bread_Mouth10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -15.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -0.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -12.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 3.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -15.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 5.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 9.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "x": -0.48, "y": 4.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "x": 1, "y": -1.71, "curve": "stepped"}, {"time": 1.5, "x": 1, "y": -1.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Bread_Mouth13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 15.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -4.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 13.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -5.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 15.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -4.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 13.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -9.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -16.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 6.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -9.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 10.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 4.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -25.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -6.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -21.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -23.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -13.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Bread_Mouth12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 16.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 12.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -2.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 23.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 17.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard40": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -9.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -12.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -6.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -13.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -6.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard16": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -15.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -13.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -3.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -17.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -6.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard41": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 8.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -3.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 5.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 6.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 1.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 9.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard42": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 22.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 8.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -7.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -8.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard38": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 8.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -0.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 6.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 6.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard39": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 10.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -11.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 2.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -6.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 9.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard46": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 8.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 8.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 2.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard43": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -1.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 8.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 2.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard44": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -3.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 2.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 8.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.94, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 10.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 4.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard45": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -12.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 2.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -14.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -0.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -14.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -12.17, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard47": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 0.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 3.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -1.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard48": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 2.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -10.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 2.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -9.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": 2.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -8.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard35": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -10.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}], "translate": [{"time": 0.8333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "x": 3.71, "y": -0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "x": 0, "y": 0}]}, "Beard31": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard28": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard24": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard20": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard17": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": -11.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -2.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -15.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -7.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -22.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard36": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 1.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -9.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -5.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -10.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -6.38, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -12.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard37": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 14.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -3.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 7.64, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -6.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -5.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard32": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 1.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -6.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -11.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -10.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard34": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 19.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 15.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -13.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -9.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard33": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 13.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -2.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": 2.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 15.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard29": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 4.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 2.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": -5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 2.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -3.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard30": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": 18.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 19.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 23.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 4.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard25": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 2.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 1.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -4.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard27": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 20.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -0.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 19.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -3.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 15.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 0.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard26": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 9.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -1.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 7.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -5.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -5.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard21": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 8.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 10.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 1.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 8.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard23": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 17.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -1.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 17.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -1.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 19.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard22": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 11.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -6.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 5.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -7.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard18": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": -1.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 8.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 1.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 9.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard19": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 31.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 4.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.04, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 5.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 27.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 9.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard68": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard64": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard61": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard57": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard50": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard53": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 17.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 4.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 24.11, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 16.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -2.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard51": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -3.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 8.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 9.49, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 5.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 14.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 8.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard52": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -9.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 7.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -9.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -5.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 6.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard54": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 1.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 3.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -2.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 2.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": -2.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard55": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 10.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 11.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 2.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 13.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": 4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard56": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -39.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 5.71, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -9.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -0.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": 6.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard58": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -3.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -0.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -6.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.16, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard60": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 9.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -0.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard59": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -8.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 8.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 7.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 1.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 6.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard62": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -11.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -7.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": 4.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -6.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 2.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard63": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -18.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -0.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -19.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "angle": -0.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": -21.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -6.13, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -21.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard65": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -5.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -11.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -7.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -12.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -5.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -10.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard67": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 3.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -7.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 1.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -7.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -15.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard66": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -1.84, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -7.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -0.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": -5.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 1.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard69": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -12.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard70": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 0.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -12.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -1.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": -15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -3.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -14.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -11.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 6.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 1.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -26.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -6.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -18.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -14.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 12.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -0.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -4.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -16.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -20.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -12.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -11.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 4.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": -12.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -11.52, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 4.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 6.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -8.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 1.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -3.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -4.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 7.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -19.15, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 11.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -22.05, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 10.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -24.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 11.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -7.2, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -13.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 6.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard74": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard75": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard76": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard77": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard78": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard79": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 19.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -16.68, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 21.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 11.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard80": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -6.78, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard81": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": 8.88, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 14.87, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 20.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 26.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard82": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -4.79, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard83": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -3.14, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Beard84": {"rotate": [{"time": 0, "angle": 0, "curve": [0.268, 0, 0.618, 0.42]}, {"time": 0.2, "angle": -10.76, "curve": [0.346, 0.38, 0.757, 1]}, {"time": 0.5, "angle": 4.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -1.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 4.19, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -1.39, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 4.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 0}]}, "Fx_BG": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "x": 2.05, "y": 2.05}]}, "Fx_BG2": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 2.05, "y": 2.05}]}, "Fx_BG3": {"scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.8, "y": 1.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 2.05, "y": 2.05}]}, "Fx_Rubi2": {"rotate": [{"time": 0, "angle": 146.76}], "translate": [{"time": 0, "x": -2.4, "y": -0.66}], "scale": [{"time": 0, "x": 0.48, "y": 0.349}]}, "Fx_Rubi3": {"rotate": [{"time": 0, "angle": 19.44}], "translate": [{"time": 0, "x": 1.42, "y": -0.47}], "scale": [{"time": 0, "x": 0.48, "y": 0.349}]}, "Fx_Rubi4": {"rotate": [{"time": 0, "angle": -15.1}]}, "Sparkle": {"rotate": [{"time": 0.5, "angle": 0}, {"time": 1.1, "angle": 111.55}], "scale": [{"time": 0.5, "x": 1, "y": 1}, {"time": 0.8, "x": 1.208, "y": 1.208}, {"time": 1.1, "x": 0.63, "y": 0.63}]}, "Sparkle2": {"rotate": [{"time": 0.3667, "angle": 0}, {"time": 0.9667, "angle": 111.55}], "scale": [{"time": 0.3667, "x": 1, "y": 1}, {"time": 0.6667, "x": 1.208, "y": 1.208}, {"time": 0.9667, "x": 0.63, "y": 0.63}]}, "Sparkle3": {"rotate": [{"time": 0.6, "angle": 0}, {"time": 1.2, "angle": 111.55}], "scale": [{"time": 0.6, "x": 1, "y": 1}, {"time": 0.9, "x": 1.208, "y": 1.208}, {"time": 1.2, "x": 0.63, "y": 0.63}]}, "Sparkle4": {"rotate": [{"time": 0.7333, "angle": 0}, {"time": 1.3333, "angle": 111.55}], "scale": [{"time": 0.7333, "x": 1, "y": 1}, {"time": 1.0333, "x": 1.208, "y": 1.208}, {"time": 1.3333, "x": 0.63, "y": 0.63}]}, "Sparkle5": {"rotate": [{"time": 0.9667, "angle": 0}, {"time": 1.5667, "angle": 111.55}], "scale": [{"time": 0.9667, "x": 1, "y": 1}, {"time": 1.2667, "x": 1.208, "y": 1.208}, {"time": 1.5667, "x": 0.63, "y": 0.63}]}, "Sparkle6": {"rotate": [{"time": 0.2667, "angle": 0}, {"time": 0.8667, "angle": 111.55, "curve": "stepped"}, {"time": 1.2, "angle": -36.25}, {"time": 1.8, "angle": 111.55}], "translate": [{"time": 1.2, "x": -5.92, "y": 11.25}], "scale": [{"time": 0.2667, "x": 1, "y": 1}, {"time": 0.5667, "x": 1.208, "y": 1.208}, {"time": 0.8667, "x": 0.63, "y": 0.63, "curve": "stepped"}, {"time": 1.2, "x": 1, "y": 1}, {"time": 1.5, "x": 1.208, "y": 1.208}, {"time": 1.8, "x": 0.63, "y": 0.63}]}, "Sparkle7": {"rotate": [{"time": 0.8333, "angle": 0}, {"time": 1.4333, "angle": 111.55}], "scale": [{"time": 0.8333, "x": 1, "y": 1}, {"time": 1.1333, "x": 1.208, "y": 1.208}, {"time": 1.4333, "x": 0.63, "y": 0.63}]}, "Sparkle8": {"rotate": [{"time": 0.7667, "angle": 0}, {"time": 1.3667, "angle": 111.55}], "translate": [{"time": 0.7667, "x": -11.04, "y": 0.94}], "scale": [{"time": 0.7667, "x": 1, "y": 1}, {"time": 1.0667, "x": 1.208, "y": 1.208}, {"time": 1.3667, "x": 0.63, "y": 0.63}]}}}}}