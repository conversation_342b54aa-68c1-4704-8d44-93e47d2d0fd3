{"skeleton": {"hash": "qDZiAl1/xobdpKVsRqXv3VKSWyY", "spine": "3.7.94", "width": 136.76, "height": 139.68, "images": "./image/", "audio": "F:/DIEM/tulinh/NEW_VERSION4720/Animation/TL Symbol Phoenix Gold/TL Symbol Phoenix Gold/.."}, "bones": [{"name": "root"}, {"name": "All", "parent": "root", "x": -2.11, "y": 0.99}, {"name": "Body", "parent": "All", "x": -39.21, "y": -22.82, "color": "ff0000ff"}, {"name": "Wing1", "parent": "Body", "length": 21.42, "rotation": -0.91, "x": 5.85, "y": 6.19, "color": "ff00b8ff"}, {"name": "Wing2", "parent": "Wing1", "length": 19.68, "rotation": 100.86, "x": 21.42, "color": "ff00b8ff"}, {"name": "Wing4", "parent": "Wing2", "x": 20.4, "y": 0.22, "color": "ff00b8ff"}, {"name": "Wing5", "parent": "Wing4", "length": 40.16, "rotation": -152.16, "x": -0.22, "y": -0.74, "color": "ff00b8ff"}, {"name": "Wing6", "parent": "Wing4", "length": 32.45, "rotation": -122.39, "x": 0.61, "y": -0.9, "color": "ff00b8ff"}, {"name": "Wing7", "parent": "Wing5", "length": 34.28, "rotation": 20.03, "x": 49.64, "y": -0.31, "color": "ff00b8ff"}, {"name": "Wing8", "parent": "Wing6", "length": 34.07, "rotation": -6.45, "x": 33.84, "y": 0.05, "color": "ff00b8ff"}, {"name": "3D_L", "parent": "Body", "x": -18.43, "y": -2.3, "color": "0070ffff"}, {"name": "3D_T", "parent": "Body", "x": 3.58, "y": 26.88, "color": "0070ffff"}, {"name": "3D_B", "parent": "Body", "x": 23.04, "y": -34.16, "color": "0070ffff"}, {"name": "3D_R", "parent": "Body", "x": 62.21, "y": -8.96, "color": "0070ffff"}, {"name": "Body2", "parent": "Body", "length": 17.24, "rotation": -49.21, "x": 0.51, "y": -0.26, "color": "ff0000ff"}, {"name": "Body3", "parent": "Body2", "length": 22.55, "rotation": 36.76, "x": 17.24, "color": "ff0000ff"}, {"name": "Body4", "parent": "Body3", "length": 34.31, "rotation": 13.31, "x": 22.55, "color": "ff0000ff"}, {"name": "Wing9", "parent": "Wing1", "length": 17.87, "rotation": -51.07, "x": 10.43, "y": -9.61, "color": "fd00f1ff"}, {"name": "Body5", "parent": "Body", "length": 17.85, "rotation": 75.68, "x": -0.7, "y": 3.94, "color": "ff0000ff"}, {"name": "Body6", "parent": "Body5", "length": 16.43, "rotation": -19.72, "x": 17.85, "color": "ff0000ff"}, {"name": "Body7", "parent": "Body6", "length": 14.74, "rotation": 36.91, "x": 16.43, "color": "ff0000ff"}, {"name": "Wing10", "parent": "Body", "length": 37.14, "rotation": 129.56, "x": 4.91, "y": 7.68, "color": "a714ccff"}, {"name": "Wing3", "parent": "Wing10", "length": 45.54, "rotation": 169.91, "x": 36.34, "y": 0.98, "color": "a714ccff"}, {"name": "Wing11", "parent": "Wing3", "length": 39.43, "rotation": 31.65, "x": 45.54, "color": "a714ccff"}, {"name": "Head", "parent": "Body7", "rotation": -92.86, "x": 14.36, "y": 1.14, "color": "ff0000ff"}, {"name": "Mouth", "parent": "Head", "length": 17.82, "rotation": -159.55, "x": -3.96, "color": "ff0000ff"}, {"name": "Head3", "parent": "Head", "length": 13.88, "rotation": -69.79, "x": 10.47, "y": -3.68, "color": "ff0000ff"}, {"name": "Head4", "parent": "Head3", "length": 12.65, "rotation": 20.18, "x": 13.88, "color": "ff0000ff"}, {"name": "Head5", "parent": "Head4", "length": 11.12, "rotation": 64.28, "x": 12.65, "color": "ff0000ff"}, {"name": "Head6", "parent": "Head5", "length": 9.66, "rotation": 70.21, "x": 11.12, "color": "ff0000ff"}, {"name": "Tail1", "parent": "Body", "length": 24.15, "rotation": 26.23, "x": 43.02, "y": -10.67, "color": "00ff1fff"}, {"name": "Tail2", "parent": "Tail1", "length": 17.78, "rotation": 21.63, "x": 24.15, "color": "00ff1fff"}, {"name": "Tail3", "parent": "Tail2", "length": 24.65, "rotation": 35.56, "x": 17.78, "color": "00ff1fff"}, {"name": "Tail4", "parent": "Tail3", "length": 24.5, "rotation": 8.05, "x": 24.65, "color": "00ff1fff"}, {"name": "Tail5", "parent": "Body", "length": 24.03, "rotation": 83.25, "x": 63.43, "y": 6.29, "color": "00ff1fff"}, {"name": "Tail6", "parent": "Tail5", "length": 22.69, "rotation": 1.99, "x": 24.03, "color": "00ff1fff"}, {"name": "Tail7", "parent": "Tail6", "length": 19.32, "rotation": 49.11, "x": 22.69, "color": "00ff1fff"}, {"name": "Tail8", "parent": "Tail7", "length": 29.8, "rotation": 16.66, "x": 19.32, "color": "00ff1fff"}, {"name": "Tail9", "parent": "Body", "length": 16.8, "rotation": 20.81, "x": 61.55, "y": -5.33, "color": "00ff1fff"}, {"name": "Tail10", "parent": "Tail9", "length": 15.85, "rotation": 12.88, "x": 16.8, "color": "00ff1fff"}, {"name": "Tail11", "parent": "Tail10", "length": 10.11, "rotation": 30.54, "x": 15.85, "color": "00ff1fff"}, {"name": "Tail12", "parent": "Tail11", "length": 21.57, "rotation": 4.43, "x": 10.11, "color": "00ff1fff"}, {"name": "Tail13", "parent": "Body", "length": 17.91, "rotation": -2.01, "x": 56.52, "y": -8.78, "color": "00ff1fff"}, {"name": "Tail14", "parent": "Tail13", "length": 15.61, "rotation": 17.16, "x": 17.91, "color": "00ff1fff"}, {"name": "Tail15", "parent": "Tail14", "length": 21.1, "rotation": 31.65, "x": 15.61, "color": "00ff1fff"}, {"name": "Tail16", "parent": "Body", "length": 19.71, "rotation": 59.35, "x": 77.25, "y": 13.83, "color": "00ff1fff"}, {"name": "Tail17", "parent": "Tail16", "length": 16.94, "rotation": 19.97, "x": 19.71, "color": "00ff1fff"}, {"name": "Tail18", "parent": "Tail17", "length": 13.94, "rotation": 46.52, "x": 16.94, "color": "00ff1fff"}, {"name": "Tail19", "parent": "Tail18", "length": 25.11, "rotation": 6.63, "x": 13.94, "color": "00ff1fff"}, {"name": "3D", "parent": "Body", "x": 39.21, "y": 113.99, "color": "ff7500ff"}, {"name": "3D_FaceT", "parent": "Head", "x": -11.69, "y": 12.1, "color": "ff0000ff"}, {"name": "3D_FaceL", "parent": "Head", "x": -22.99, "y": -7.05, "color": "ff0000ff"}, {"name": "3D_Face", "parent": "Head", "x": -11.36, "y": 0.55, "color": "ff0000ff"}, {"name": "Head2", "parent": "Head", "length": 13.26, "rotation": -163.57, "x": -1.75, "y": 6.33, "color": "ff0000ff"}, {"name": "Head7", "parent": "Head2", "length": 12.45, "rotation": 28.57, "x": 13.26, "color": "ff0000ff"}, {"name": "Wing12", "parent": "Wing10", "length": 57.58, "rotation": -173.59, "x": 35.09, "y": 0.07, "color": "a714ccff"}, {"name": "FX", "parent": "All", "scaleX": 2.025, "scaleY": 2.025}, {"name": "FX2", "parent": "All", "scaleX": 2.025, "scaleY": 2.025}, {"name": "Fx_br", "parent": "Head", "x": -7.93, "y": 2.38}, {"name": "Star", "parent": "All", "x": 28.45, "y": -34.71}, {"name": "Star2", "parent": "All", "x": -39.57, "y": -73.45, "scaleX": 0.988, "scaleY": 0.988}, {"name": "Star3", "parent": "All", "x": 13.32, "y": -25.93, "scaleX": 0.872, "scaleY": 0.872}, {"name": "Star4", "parent": "All", "x": 45.95, "y": 5.1, "scaleX": 0.789, "scaleY": 0.789}, {"name": "Star5", "parent": "All", "x": -59.95, "y": -63.07, "scaleX": 0.915, "scaleY": 0.915}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000", "parent": "Wing8", "rotation": -81.62, "x": -37.2, "y": -10.77}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0", "parent": "All", "rotation": -78.52, "x": -48.69, "y": -5.61}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1", "parent": "Wing8", "rotation": -81.62, "x": -37.2, "y": -10.77}], "slots": [{"name": "BG", "bone": "All", "attachment": "BG"}, {"name": "Fire/Fire 01", "bone": "FX"}, {"name": "Fire/Fire 1", "bone": "FX2"}, {"name": "Tail3", "bone": "Tail16", "attachment": "Tail1"}, {"name": "Tail2", "bone": "Tail5", "attachment": "Tail1"}, {"name": "Tail1", "bone": "Tail1", "attachment": "Tail1"}, {"name": "Tail6", "bone": "Tail1"}, {"name": "Wing3", "bone": "Wing3", "attachment": "Wing2"}, {"name": "Wing4", "bone": "Wing10", "attachment": "Wing4"}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0", "bone": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0"}, {"name": "Body", "bone": "Body", "attachment": "Body"}, {"name": "Tail5", "bone": "Tail9", "attachment": "Tail5"}, {"name": "Tail4", "bone": "Tail13", "attachment": "Tail5"}, {"name": "Mouth", "bone": "Mouth", "attachment": "Mouth"}, {"name": "Head", "bone": "Head3", "attachment": "Head"}, {"name": "Wing1", "bone": "Wing9", "attachment": "Wing1"}, {"name": "Wing2", "bone": "Wing4", "attachment": "Wing2"}, {"name": "Wing6", "bone": "Wing4"}, {"name": "Wing5", "bone": "Wing4", "attachment": "Wing2"}, {"name": "Eye Bright", "bone": "Fx_br", "blend": "additive"}, {"name": "Sparkle", "bone": "Star"}, {"name": "Sparkle5", "bone": "Star5"}, {"name": "Sparkle3", "bone": "Star3"}, {"name": "Sparkle4", "bone": "Star4"}, {"name": "Sparkle2", "bone": "Star2"}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000", "bone": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1", "bone": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1"}, {"name": "demo_phuong_G", "bone": "root", "color": "ffffff9e"}], "transform": [{"name": "3D_B", "order": 0, "bones": ["3D_B"], "target": "3D", "x": -16.16, "y": -148.16, "rotateMix": 0, "translateMix": -0.05, "scaleMix": 0, "shearMix": 0}, {"name": "3D_FaceL", "order": 13, "bones": ["3D_FaceL"], "target": "3D_Face", "x": -11.62, "y": -7.61, "rotateMix": 0, "translateMix": 0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_FaceL2", "order": 15, "bones": ["Head3"], "target": "3D_FaceL", "rotation": -69.79, "x": 33.46, "y": 3.37, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_FaceT", "order": 14, "bones": ["3D_FaceT"], "target": "3D_Face", "x": -0.32, "y": 11.55, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_FaceT2", "order": 16, "bones": ["Head2"], "target": "3D_FaceT", "rotation": -163.57, "x": 9.94, "y": -5.77, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_L", "order": 1, "bones": ["3D_L"], "target": "3D", "x": -57.64, "y": -116.3, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_R", "order": 2, "bones": ["3D_R"], "target": "3D", "x": 23, "y": -122.95, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}, {"name": "3D_R1", "order": 4, "bones": ["Body2"], "target": "3D_R", "rotation": -49.21, "x": -61.7, "y": 8.7, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_R2", "order": 5, "bones": ["Body3"], "target": "3D_R", "rotation": -12.46, "x": -50.43, "y": -4.35, "rotateMix": 0, "translateMix": 0.3, "scaleMix": 0, "shearMix": 0}, {"name": "3D_R3", "order": 6, "bones": ["Body4"], "target": "3D_R", "rotation": 0.86, "x": -28.42, "y": -9.22, "rotateMix": 0, "translateMix": 0.4, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T", "order": 3, "bones": ["3D_T"], "target": "3D", "x": -35.62, "y": -87.11, "rotateMix": 0, "translateMix": 0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T1", "order": 10, "bones": ["Body5"], "target": "3D_T", "rotation": 75.68, "x": -4.29, "y": -22.94, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T2", "order": 11, "bones": ["Body6"], "target": "3D_T", "rotation": 55.95, "x": 0.13, "y": -5.64, "rotateMix": 0, "translateMix": 0.3, "scaleMix": 0, "shearMix": 0}, {"name": "3D_T3", "order": 12, "bones": ["Body7"], "target": "3D_T", "rotation": 92.86, "x": 9.33, "y": 7.97, "shearY": -360, "rotateMix": 0, "translateMix": 0.4, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail1", "order": 17, "bones": ["Tail5"], "target": "3D_R", "rotation": 83.25, "x": 1.22, "y": 15.25, "rotateMix": 0, "translateMix": -0.4, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail2", "order": 18, "bones": ["Tail1"], "target": "3D_R", "rotation": 26.23, "x": -19.19, "y": -1.71, "rotateMix": 0, "translateMix": -0.3, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail3", "order": 19, "bones": ["Tail16"], "target": "3D_R", "rotation": 59.35, "x": 15.04, "y": 22.79, "rotateMix": 0, "translateMix": -0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail4", "order": 20, "bones": ["Tail9"], "target": "3D_R", "rotation": 20.81, "x": -0.66, "y": 3.63, "rotateMix": 0, "translateMix": -0.4, "scaleMix": 0, "shearMix": 0}, {"name": "3D_Tail5", "order": 21, "bones": ["Tail13"], "target": "3D_R", "rotation": -2.01, "x": -5.69, "y": 0.18, "rotateMix": 0, "translateMix": -0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_WingL", "order": 8, "bones": ["Wing10"], "target": "3D_T", "rotation": 129.56, "x": 1.33, "y": -19.2, "shearY": -360, "rotateMix": 0, "translateMix": 0.2, "scaleMix": 0, "shearMix": 0}, {"name": "3D_WingR", "order": 7, "bones": ["Wing1"], "target": "3D_T", "rotation": -0.91, "x": 2.27, "y": -20.69, "rotateMix": 0, "translateMix": -0.1, "scaleMix": 0, "shearMix": 0}, {"name": "3D_WingR2", "order": 9, "bones": ["Wing2"], "target": "3D_T", "rotation": 99.95, "x": 23.69, "y": -21.03, "shearY": -360, "rotateMix": 0, "translateMix": -0.15, "scaleMix": 0, "shearMix": 0}], "skins": {"default": {"BG": {"BG": {"width": 122, "height": 121}}, "Body": {"Body": {"type": "mesh", "uvs": [0.45987, 0.08449, 0.46165, 0.20639, 0.40627, 0.34066, 0.47058, 0.45726, 0.84754, 0.43959, 1, 0.54736, 1, 0.76465, 0.81895, 1, 0.52418, 1, 0.18117, 0.90599, 0, 0.74345, 0, 0.48906, 0.03289, 0.30532, 0.23298, 0.21522, 0.27764, 0.11452, 0.26335, 0, 0.33055, 0, 0.37875, 0, 0.44915, 0, 0.2562, 0.40956, 0.23655, 0.57209, 0.37769, 0.67632, 0.61887, 0.75229, 0.8654, 0.71519, 0.18474, 0.34772, 0.07755, 0.49789, 0.1115, 0.67279, 0.2705, 0.82472, 0.58849, 0.90245, 0.29718, 0.29058, 0.36763, 0.21541, 0.39915, 0.10541, 0.35094, 0.11458, 0.31943, 0.21175, 0.24156, 0.27591, 0.09238, 0.37711, 0.33723, 0.40385], "triangles": [8, 28, 7, 28, 22, 7, 7, 23, 6, 7, 22, 23, 23, 5, 6, 22, 4, 23, 22, 3, 4, 23, 4, 5, 27, 21, 28, 28, 21, 22, 8, 27, 28, 21, 3, 22, 3, 20, 36, 21, 20, 3, 27, 20, 21, 9, 27, 8, 10, 26, 9, 9, 26, 27, 26, 20, 27, 26, 10, 11, 26, 25, 20, 19, 34, 29, 36, 20, 19, 26, 11, 25, 20, 25, 19, 19, 25, 35, 19, 35, 24, 25, 11, 35, 11, 12, 35, 24, 34, 19, 19, 29, 36, 35, 12, 24, 24, 12, 13, 36, 29, 2, 34, 24, 13, 29, 30, 2, 2, 30, 1, 29, 33, 30, 29, 34, 33, 34, 13, 33, 33, 32, 30, 30, 31, 1, 13, 14, 33, 36, 2, 3, 30, 32, 31, 33, 14, 32, 31, 0, 1, 14, 16, 32, 32, 17, 31, 32, 16, 17, 14, 15, 16, 0, 17, 18, 0, 31, 17], "vertices": [2, 20, 7.95, -7.64, 0.928, 11, 16.56, 16.29, 0.072, 5, 20, -3.02, -7.25, 0.26142, 19, 18.37, -7.61, 0.57051, 14, -11.65, 36.19, 1e-05, 15, -1.49, 46.28, 6e-05, 11, 16.72, 5.32, 0.168, 7, 19, 5.6, -10.29, 0.53909, 18, 19.65, -11.57, 0.15179, 14, -5.72, 24.56, 0.03713, 15, -3.7, 33.42, 0.02626, 16, -17.84, 38.56, 0.00381, 11, 11.79, -6.76, 0.16192, 10, 33.81, 22.42, 0.08, 6, 19, 0.11, -20.9, 0.09865, 18, 10.9, -19.72, 0.21037, 14, 5.97, 22.04, 0.20218, 15, 4.15, 24.41, 0.26049, 16, -12.28, 27.98, 0.08431, 11, 17.51, -17.26, 0.144, 5, 19, 20.21, -47.81, 1e-05, 18, 20.74, -51.83, 0.00197, 14, 26.68, 48.48, 0.00342, 15, 36.57, 33.2, 0.09338, 16, 21.29, 29.07, 0.90122, 2, 15, 51.91, 26.65, 0.01123, 16, 34.71, 19.17, 0.98877, 1, 16, 34.42, -0.38, 1, 3, 15, 44.97, -16.6, 0.01058, 16, 17.99, -21.32, 0.79742, 12, 29.06, -5.06, 0.192, 4, 14, 46.07, -6.26, 0.00091, 15, 19.35, -22.26, 0.33038, 16, -8.24, -20.93, 0.1407, 12, 2.83, -5.06, 0.528, 4, 18, -34.6, -4.75, 0.00169, 14, 19.72, -23.84, 0.61393, 15, -12.28, -20.59, 0.24839, 12, -27.7, 3.4, 0.136, 5, 18, -24.42, 14.49, 0.14474, 14, -1.89, -26.5, 0.65875, 15, -31.18, -9.78, 0.00291, 11, -24.37, -43.02, 0.1536, 10, -2.35, -13.83, 0.04, 5, 19, -25.71, 12.2, 0.00313, 18, -2.23, 20.16, 0.57879, 14, -19.22, -11.54, 0.108, 11, -24.37, -20.12, 0.19008, 10, -2.35, 9.06, 0.12, 3, 19, -10.37, 19.03, 0.11991, 18, 14.51, 21.41, 0.73609, 11, -21.44, -3.58, 0.144, 3, 20, -2.79, 13.12, 0.03313, 19, 6.32, 8.81, 0.91491, 18, 26.77, 6.16, 0.05196, 2, 20, 6.06, 8.7, 0.69397, 19, 16.05, 10.59, 0.30603, 2, 20, 16.42, 9.45, 0.99485, 19, 23.88, 17.42, 0.00515, 3, 20, 16.12, 3.48, 0.89306, 19, 27.23, 12.46, 0.00294, 10, 27.07, 53.08, 0.104, 4, 20, 15.9, -0.8, 0.77992, 19, 29.63, 8.91, 0.00152, 11, 9.34, 23.9, 0.12, 10, 31.36, 53.08, 0.09856, 2, 20, 15.59, -7.06, 0.928, 11, 15.61, 23.9, 0.072, 6, 19, -7.02, -2.69, 0.00038, 18, 10.34, -0.17, 0.7671, 14, -9.75, 10.4, 0.00038, 15, -15.4, 24.48, 0.00012, 16, -31.29, 32.56, 1e-05, 10, 20.45, 16.22, 0.232, 2, 14, 0.19, -0.48, 0.752, 10, 18.7, 1.59, 0.248, 7, 19, -20.86, -25.09, 0.00054, 18, -10.25, -16.58, 0.00319, 14, 15.49, 2.9, 0.34524, 15, 0.33, 3.37, 0.19011, 16, -20.84, 8.39, 0.00083, 10, 31.26, -7.79, 0.3721, 12, -10.21, 24.07, 0.088, 7, 19, -14.5, -46.71, 2e-05, 18, -11.56, -39.07, 0.00019, 14, 34.69, 14.69, 0.00036, 15, 22.77, 1.32, 0.27928, 16, 0.52, 1.24, 0.45705, 10, 52.73, -14.63, 0.1751, 12, 11.25, 17.23, 0.088, 2, 15, 43.47, 9.32, 0.00242, 16, 22.51, 4.25, 0.99758, 3, 19, -5.96, 5.69, 0.13475, 18, 14.16, 7.37, 0.61725, 10, 14.09, 21.78, 0.248, 4, 19, -22.5, 6.03, 0.00206, 18, -1.3, 13.27, 0.6907, 14, -14.11, -6.83, 0.12325, 10, 4.55, 8.27, 0.184, 4, 18, -15.8, 6.45, 0.16917, 14, -0.22, -14.83, 0.661, 15, -22.87, -1.43, 0.00182, 10, 7.57, -7.47, 0.168, 4, 14, 19.38, -13.05, 0.31593, 15, -6.1, -11.73, 0.20266, 10, 21.72, -21.15, 0.36941, 12, -19.75, 10.71, 0.112, 3, 15, 23.05, -12.46, 0.4469, 16, -2.38, -12.24, 0.3931, 12, 8.55, 3.72, 0.16, 7, 20, -9.85, 7.75, 0.01282, 19, 3.9, 0.28, 0.55049, 18, 21.62, -1.05, 0.20412, 14, -15.47, 20.15, 0.01359, 15, -14.15, 35.72, 0.00959, 16, -27.49, 43.21, 0.00139, 10, 24.1, 26.93, 0.208, 8, 20, -3.41, 1.15, 0.19282, 19, 13.01, -1.13, 0.39981, 18, 29.72, -5.45, 0.05693, 14, -16.5, 29.32, 0.00379, 15, -9.49, 43.68, 0.0027, 16, -21.12, 49.88, 0.00039, 11, 8.35, 4.51, 0.23738, 10, 30.37, 33.69, 0.10618, 8, 20, 6.34, -2.14, 0.55046, 19, 22.79, 2.09, 0.26429, 18, 40.01, -5.72, 0.01493, 14, -22.16, 37.91, 0.00099, 15, -8.89, 53.95, 0.00071, 16, -18.16, 59.74, 0.0001, 11, 11.16, 14.41, 0.104, 10, 33.17, 43.59, 0.06451, 8, 20, 5.73, 2.18, 0.68203, 19, 19.7, 5.18, 0.20711, 18, 38.15, -1.77, 0.00817, 14, -24.34, 34.12, 0.00054, 15, -12.9, 52.22, 0.00039, 16, -22.46, 58.98, 6e-05, 11, 6.87, 13.58, 0.032, 10, 28.88, 42.77, 0.0697, 7, 20, -2.87, 5.42, 0.21035, 19, 10.89, 2.61, 0.6668, 18, 28.98, -1.21, 0.06856, 14, -19.55, 26.29, 0.00347, 15, -13.75, 43.08, 0.00246, 16, -25.4, 50.28, 0.00035, 10, 26.08, 34.02, 0.048, 7, 20, -8.29, 12.63, 0.01592, 19, 2.22, 5.12, 0.55161, 18, 21.67, 4.07, 0.27959, 14, -19.7, 17.27, 0.00492, 15, -19.27, 35.94, 0.00347, 16, -32.42, 44.61, 0.0005, 10, 19.15, 28.25, 0.144, 5, 19, -12.76, 11.02, 0.08678, 18, 9.56, 14.68, 0.68745, 14, -21.48, 1.27, 0.03883, 11, -16.15, -10.04, 0.05805, 10, 5.87, 19.14, 0.1289, 7, 19, -2.55, -8.38, 0.09946, 18, 12.62, -7.03, 0.28843, 14, -5.42, 16.19, 0.03554, 15, -8.47, 26.54, 0.04228, 16, -24.07, 32.97, 0.01294, 11, 5.64, -12.45, 0.25154, 10, 27.66, 16.73, 0.26981], "hull": 19, "edges": [30, 28, 28, 26, 26, 24, 24, 22, 20, 22, 20, 18, 18, 16, 14, 16, 14, 12, 12, 10, 10, 8, 8, 6, 6, 4, 4, 2, 2, 0, 0, 36, 30, 32, 32, 34, 34, 36], "width": 87, "height": 88}}, "Eye Bright": {"Eye Bright": {"width": 24, "height": 23}}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0": {"Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1": {"Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018": {"x": 1.71, "y": 33.75, "width": 57, "height": 71}}, "Fire/Fire 01": {"Fire/Fire 01": {"width": 68, "height": 55}, "Fire/Fire 03": {"y": 0.5, "width": 86, "height": 75}, "Fire/Fire 05": {"x": 0.5, "y": 0.5, "width": 89, "height": 87}, "Fire/Fire 07": {"x": 0.5, "width": 101, "height": 94}, "Fire/Fire 09": {"width": 104, "height": 100}, "Fire/Fire 11": {"width": 104, "height": 108}, "Fire/Fire 13": {"x": 0.5, "width": 109, "height": 112}, "Fire/Fire 15": {"y": 0.5, "width": 112, "height": 113}, "Fire/Fire 17": {"y": 0.5, "width": 112, "height": 109}, "Fire/Fire 19": {"x": 0.5, "y": 0.5, "width": 105, "height": 101}}, "Fire/Fire 1": {"Fire/Fire 01": {"width": 68, "height": 55}, "Fire/Fire 03": {"y": 0.5, "width": 86, "height": 75}, "Fire/Fire 05": {"x": 0.5, "y": 0.5, "width": 89, "height": 87}, "Fire/Fire 07": {"x": 0.5, "width": 101, "height": 94}, "Fire/Fire 09": {"width": 104, "height": 100}, "Fire/Fire 11": {"width": 104, "height": 108}, "Fire/Fire 13": {"x": 0.5, "width": 109, "height": 112}, "Fire/Fire 15": {"y": 0.5, "width": 112, "height": 113}, "Fire/Fire 17": {"y": 0.5, "width": 112, "height": 109}, "Fire/Fire 19": {"x": 0.5, "y": 0.5, "width": 105, "height": 101}}, "Head": {"Head": {"type": "mesh", "uvs": [0.57845, 0.15641, 0.64823, 0.20024, 0.66146, 0.31041, 0.79982, 0.25355, 1, 0.33529, 0.91653, 0.49047, 1, 0.60893, 1, 0.75939, 0.81907, 0.82928, 0.79381, 1, 0.56281, 0.97972, 0.53012, 0.74898, 0.47959, 0.57011, 0.37853, 0.43269, 0.27746, 0.48481, 0.22092, 0.43861, 0, 0.60446, 0, 0.37583, 0.10542, 0.30357, 0.11722, 0.2828, 0.17581, 0, 0.2771, 0.03573, 0.26605, 0.18242, 0.32581, 0.18588, 0.30893, 0.04895, 0.44417, 0.10511, 0.39495, 0, 0.59886, 0, 0.65064, 0.53786, 0.78298, 0.69659, 0.94179, 0.68593, 0.18636, 0.33819, 0.25433, 0.29088, 0.32581, 0.30242, 0.27073, 0.35203, 0.15355, 0.36703, 0.19925, 0.25973, 0.31409, 0.2355, 0.41513, 0.34641, 0.399, 0.26438, 0.51994, 0.37287, 0.53936, 0.27749], "triangles": [35, 18, 31, 35, 16, 17, 35, 17, 18, 16, 35, 15, 22, 20, 21, 25, 23, 24, 37, 22, 23, 36, 20, 22, 32, 36, 22, 39, 23, 25, 37, 23, 39, 19, 20, 36, 37, 32, 22, 33, 37, 39, 32, 37, 33, 31, 19, 36, 31, 36, 32, 18, 19, 31, 33, 39, 38, 34, 32, 33, 31, 32, 34, 13, 33, 38, 34, 33, 13, 15, 31, 34, 14, 15, 34, 35, 31, 15, 13, 14, 34, 5, 3, 4, 30, 5, 6, 29, 5, 30, 30, 6, 7, 8, 30, 7, 8, 29, 30, 9, 10, 8, 11, 28, 29, 8, 10, 11, 8, 11, 29, 25, 26, 27, 0, 25, 27, 41, 39, 25, 0, 41, 25, 41, 0, 1, 41, 1, 2, 38, 39, 41, 40, 38, 41, 40, 41, 2, 28, 40, 2, 2, 3, 5, 40, 13, 38, 12, 40, 28, 12, 13, 40, 5, 28, 2, 29, 28, 5, 11, 12, 28], "vertices": [2, 26, -16.43, 7.89, 0.99957, 29, 35.87, 25.32, 0.00043, 2, 26, -12.21, 11.09, 0.99695, 29, 33.43, 20.62, 0.00305, 2, 26, -5.2, 9.41, 0.8387, 29, 26.37, 19.14, 0.1613, 4, 26, -5.6, 18.99, 0.51811, 29, 30.83, 10.65, 0.34513, 53, -27.46, 6.19, 0.00076, 51, 49.35, 15.19, 0.136, 3, 26, 3.83, 29.17, 0.34588, 29, 26.66, -2.58, 0.41412, 51, 62.15, 9.86, 0.24, 4, 26, 11.44, 20.66, 0.16694, 27, 4.83, 20.23, 0.00864, 29, 16.14, 1.85, 0.42442, 51, 56.8, -0.22, 0.4, 2, 29, 8.94, -4.14, 0.664, 51, 62.13, -7.93, 0.336, 3, 28, 15.56, -2.44, 0.1268, 29, -0.8, -5, 0.6412, 51, 62.12, -17.71, 0.232, 3, 27, 17.54, 1.2, 0.04345, 28, 3.2, -3.89, 0.78055, 51, 50.53, -22.24, 0.176, 3, 27, 24.94, -7.23, 0.33147, 28, -1.18, -14.21, 0.42853, 51, 48.9, -33.33, 0.24, 2, 27, 14.34, -17.63, 0.84402, 28, -15.15, -9.17, 0.15598, 2, 26, 18.62, -8.36, 0.14053, 27, 1.57, -9.49, 0.85947, 3, 26, 6.6, -7.36, 0.97329, 27, -9.37, -4.4, 0.00985, 53, -1.99, 20.13, 0.01686, 3, 26, -4.02, -10.33, 0.36227, 53, 1.69, 9.74, 0.62936, 54, -5.5, 14.08, 0.00838, 4, 26, -3.09, -17.58, 0.05315, 53, 8.85, 11.16, 0.7135, 54, 1.47, 11.9, 0.11335, 50, 4.6, -18.98, 0.12, 4, 26, -7.16, -19.93, 0.01331, 53, 11.48, 7.25, 0.50009, 54, 1.91, 7.22, 0.37461, 50, 0.98, -15.97, 0.112, 2, 54, 19.53, 4.85, 0.704, 50, -13.16, -26.75, 0.296, 2, 54, 9.02, -5.66, 0.84, 50, -13.16, -11.89, 0.16, 3, 53, 16.08, -3.26, 0.25457, 54, 0.93, -4.21, 0.67343, 50, -6.41, -7.2, 0.072, 3, 53, 14.98, -4.34, 0.55706, 54, -0.56, -4.63, 0.39494, 50, -5.66, -5.85, 0.048, 2, 53, 6.18, -20.91, 0.96, 50, -1.91, 12.54, 0.04, 1, 53, 0.62, -16.85, 1, 2, 53, 4, -7.9, 0.8, 50, 3.87, 0.68, 0.2, 3, 26, -20.24, -7.94, 0.07796, 53, 0.39, -6.6, 0.88204, 50, 7.7, 0.45, 0.04, 3, 26, -28.96, -5.87, 0.27469, 53, -1.09, -15.45, 0.65331, 50, 6.62, 9.36, 0.072, 2, 26, -22.54, 0.99, 0.904, 50, 15.3, 5.71, 0.096, 1, 26, -30.04, 0.4, 1, 2, 26, -25.52, 12.64, 0.92473, 53, -19.79, -13.25, 0.07527, 5, 26, 8.43, 3.63, 0.76509, 27, -3.87, 5.29, 0.04606, 28, -2.4, 17.17, 0.00081, 29, 11.58, 18.53, 0.06004, 51, 39.78, -3.28, 0.128, 5, 26, 21.04, 8, 0.01812, 27, 9.48, 5.04, 0.36544, 28, 3.16, 5.04, 0.32267, 29, 2.05, 9.19, 0.12576, 51, 48.23, -13.61, 0.168, 2, 29, 3.63, -0.87, 0.696, 51, 58.4, -12.93, 0.304, 3, 26, -14.05, -19.74, 6e-05, 53, 11.75, 0.37, 0.78394, 50, -1.23, -9.45, 0.216, 2, 53, 6.71, -1.35, 0.856, 50, 3.12, -6.37, 0.144, 3, 26, -13.13, -10.56, 0.00578, 53, 2.53, 0.66, 0.87422, 50, 7.69, -7.12, 0.12, 4, 26, -11.33, -14.99, 0.01414, 53, 6.83, 2.76, 0.8423, 54, -4.33, 5.5, 0.02356, 50, 4.17, -10.35, 0.12, 4, 26, -13.02, -22.36, 4e-05, 53, 14.3, 1.57, 0.06194, 54, 1.67, 0.88, 0.81802, 50, -3.33, -11.32, 0.12, 3, 53, 9.52, -4.29, 0.75987, 54, -5.34, -1.98, 0.00678, 50, -0.41, -4.35, 0.23335, 1, 53, 2.02, -3.72, 1, 3, 26, -8.47, -6.19, 0.55134, 53, -2.14, 5.02, 0.24866, 50, 13.42, -9.97, 0.2, 3, 26, -13.83, -5.31, 0.39495, 53, -2.66, -0.39, 0.53305, 50, 12.38, -4.64, 0.072, 4, 26, -4.53, -0.5, 0.87306, 29, 21.53, 27.81, 0.0056, 53, -8.09, 8.57, 0.07334, 51, 31.43, 7.46, 0.048, 5, 26, -9.92, 2.82, 0.82593, 29, 27.81, 27.12, 0.00291, 53, -11.05, 2.98, 0.05656, 50, 21.37, -5.5, 0.09105, 51, 32.68, 13.65, 0.02356], "hull": 28, "edges": [36, 34, 32, 34, 32, 30, 30, 28, 28, 26, 26, 24, 24, 22, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 22, 20, 20, 18, 36, 38, 38, 40, 50, 52, 52, 54, 0, 54, 42, 44, 44, 46, 50, 48, 46, 48, 40, 42], "width": 64, "height": 65}}, "Mouth": {"Mouth": {"x": 8.21, "y": -0.54, "rotation": 159.55, "width": 20, "height": 10}}, "Sparkle": {"Sparkle": {"width": 46, "height": 49}}, "Sparkle2": {"Sparkle": {"width": 46, "height": 49}}, "Sparkle3": {"Sparkle": {"width": 46, "height": 49}}, "Sparkle4": {"Sparkle": {"width": 46, "height": 49}}, "Sparkle5": {"Sparkle": {"width": 46, "height": 49}}, "Tail1": {"Tail1": {"type": "mesh", "uvs": [0, 0, 0.3787, 0, 1, 0.26044, 1, 0.71477, 0.47515, 1, 0.15615, 1, 0.28968, 0.74377, 0.38612, 0.58589, 0.27484, 0.44089, 0, 0.27978], "triangles": [7, 8, 1, 8, 9, 1, 9, 0, 1, 2, 7, 1, 5, 6, 4, 6, 7, 4, 4, 7, 3, 7, 2, 3], "vertices": [1, 33, 27.41, 0.1, 1, 2, 32, 42.79, -9.64, 0.00247, 33, 16.6, -12.09, 0.99753, 2, 31, 33.38, -10.57, 0.01689, 32, 6.54, -17.67, 0.98311, 2, 30, 17.88, -15.57, 0.86577, 31, -11.57, -12.17, 0.13423, 1, 30, -17.04, -6.23, 1, 1, 30, -22.54, 6.33, 1, 2, 30, 2.99, 11.25, 0.9801, 31, -15.52, 18.26, 0.0199, 3, 30, 18.98, 13.72, 0.43081, 31, 0.25, 14.67, 0.50841, 32, -5.74, 22.13, 0.06078, 4, 30, 30.2, 23.87, 0.00448, 31, 14.42, 19.96, 0.39481, 32, 8.87, 18.19, 0.54594, 33, -13.08, 20.22, 0.05477, 3, 31, 29.94, 32.33, 0.02217, 32, 28.69, 19.23, 0.28961, 33, 6.69, 18.48, 0.68822], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 43, "height": 99}}, "Tail2": {"Tail1": {"type": "mesh", "uvs": [0, 0, 0.3787, 0, 1, 0.26044, 1, 0.71477, 0.47515, 1, 0.15615, 1, 0.28968, 0.74377, 0.38612, 0.58589, 0.27484, 0.44089, 0, 0.27978], "triangles": [2, 7, 1, 7, 8, 1, 8, 9, 1, 9, 0, 1, 7, 2, 3, 5, 6, 4, 6, 7, 4, 4, 7, 3], "vertices": [3, 34, 79.18, 54.3, 0, 35, 57.01, 52.35, 0, 37, 43.32, -4.27, 1, 4, 34, 84.64, 38.95, 1e-05, 35, 61.93, 36.83, 4e-05, 36, 53.53, -5.55, 9e-05, 37, 31.18, -15.13, 0.99987, 4, 34, 69.3, 5.14, 7e-05, 35, 45.42, 3.57, 0.00043, 36, 17.58, -14.85, 0.37219, 37, -5.92, -13.73, 0.62731, 2, 34, 26.92, -9.93, 0.40163, 35, 2.54, -10.02, 0.59837, 1, 34, -7.25, 1.87, 1, 1, 34, -11.84, 14.8, 1, 3, 34, 13.98, 17.89, 0.95408, 35, -9.42, 18.23, 0.03046, 36, -7.24, 36.2, 0.01546, 4, 34, 30.1, 19.22, 0.35381, 35, 6.73, 19, 0.37805, 36, 3.91, 24.5, 0.23768, 37, -7.73, 27.89, 0.03045, 4, 34, 42.02, 28.54, 0.03967, 35, 18.97, 27.9, 0.13999, 36, 18.65, 21.07, 0.422, 37, 5.4, 20.38, 0.39834, 4, 34, 53.09, 45.02, 6e-05, 35, 30.6, 43.98, 0.00277, 36, 38.43, 22.81, 0.04941, 37, 24.85, 16.37, 0.94777], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 43, "height": 99}}, "Tail3": {"Tail1": {"type": "mesh", "uvs": [0, 0, 0.3787, 0, 1, 0.26044, 1, 0.71477, 0.47515, 1, 0.15615, 1, 0.28968, 0.74377, 0.38612, 0.58589, 0.27484, 0.44089, 0, 0.27978], "triangles": [2, 7, 1, 7, 8, 1, 8, 9, 1, 9, 0, 1, 7, 2, 3, 5, 6, 4, 6, 7, 4, 4, 7, 3], "vertices": [1, 48, 32.54, -5.87, 1, 2, 47, 39.29, -11.57, 0.00151, 48, 23.84, -14.42, 0.99849, 2, 47, 11.68, -15.02, 0.87011, 48, -3.98, -14.66, 0.12989, 2, 45, 24.19, -5.1, 0.23079, 46, 2.47, -6.33, 0.76921, 1, 45, -2.39, 0.09, 1, 1, 45, -7.15, 9.2, 1, 4, 45, 11.69, 14.18, 0.84618, 46, -2.7, 16.07, 0.12918, 47, -1.85, 25.31, 0.0242, 48, -12.76, 26.96, 0.00044, 4, 45, 23.5, 16.85, 0.19515, 46, 9.32, 14.54, 0.46225, 47, 5.32, 15.53, 0.27272, 48, -6.78, 16.43, 0.06987, 4, 45, 31.38, 25.01, 0.00848, 46, 19.51, 19.52, 0.09327, 47, 15.94, 11.57, 0.28319, 48, 3.32, 11.26, 0.61505, 3, 46, 30.18, 29.89, 0, 47, 30.8, 10.96, 0, 48, 18.01, 8.94, 0.99999], "hull": 10, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 0, 18], "width": 43, "height": 99}}, "Tail4": {"Tail5": {"type": "mesh", "uvs": [0, 0, 0.33509, 0, 1, 0.30979, 1, 0.71135, 0.31338, 1, 0, 1, 0.15052, 0.73857, 0.09624, 0.4357, 0, 0.29959], "triangles": [8, 0, 1, 7, 8, 1, 7, 1, 2, 2, 6, 7, 3, 6, 2, 4, 6, 3, 5, 6, 4], "vertices": [1, 44, 27.83, 2.11, 1, 1, 44, 25.37, -4.48, 1, 2, 43, 21.89, -8.21, 0.14968, 44, 1.03, -10.28, 0.85032, 2, 42, 17.56, -14.14, 0.65817, 43, -4.51, -13.41, 0.34183, 1, 42, -6.31, -10.61, 1, 1, 42, -9.43, -4.82, 1, 2, 42, 7.5, 0.7, 0.99978, 43, -9.74, 3.74, 0.00022, 2, 43, 9.95, 8.78, 0.70904, 44, -0.22, 10.45, 0.29096, 2, 43, 18.51, 12.52, 0.12013, 44, 9.03, 9.14, 0.87987], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 21, "height": 67}}, "Tail5": {"Tail5": {"type": "mesh", "uvs": [0, 0, 0.33509, 0, 1, 0.30979, 1, 0.71135, 0.31338, 1, 0, 1, 0.15052, 0.73857, 0.09624, 0.4357, 0, 0.29959], "triangles": [2, 6, 7, 7, 1, 2, 7, 8, 1, 8, 0, 1, 3, 6, 2, 5, 6, 4, 4, 6, 3], "vertices": [1, 41, 27.39, 4.04, 1, 1, 41, 25.34, -2.69, 1, 2, 40, 12.29, -9.84, 0.37239, 41, 1.41, -9.98, 0.62761, 2, 38, 24.84, -8.97, 0.01516, 39, 5.84, -10.54, 0.98484, 1, 38, 0.84, -6.52, 1, 1, 38, -2.53, -0.87, 1, 3, 38, 14.12, 5.41, 0.8454, 39, -1.4, 5.87, 0.15016, 40, -11.88, 13.82, 0.00444, 3, 39, 17.55, 13.21, 0.16666, 40, 8.18, 10.52, 0.48661, 41, -1.11, 10.63, 0.34674, 3, 39, 25.6, 17.95, 0.0061, 40, 17.52, 10.51, 0.02954, 41, 8.2, 9.9, 0.96435], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 21, "height": 67}}, "Wing1": {"Wing1": {"type": "mesh", "uvs": [0.90303, 0.23281, 1, 0.58173, 1, 1, 0.61954, 1, 0.46018, 1, 0.1526, 0.78527, 0.06692, 0.54992, 0, 0.36608, 0.04883, 0.21585, 0.1841, 0.29339, 0.2823, 0.24977, 0.36568, 0, 0.60286, 0, 0.19151, 0.52116, 0.37124, 0.45331, 0.61027, 0.32489], "triangles": [15, 1, 3, 15, 0, 1, 3, 1, 2, 15, 11, 12, 11, 15, 10, 15, 12, 0, 15, 3, 14, 14, 10, 15, 6, 7, 13, 7, 9, 13, 13, 9, 14, 9, 10, 14, 7, 8, 9, 4, 5, 14, 5, 13, 14, 14, 3, 4, 5, 6, 13], "vertices": [1, 9, 7.07, 3.76, 1, 1, 9, 21.61, -8.93, 1, 3, 17, 51.15, 28.67, 0.29061, 3, 64.87, -31.39, 0.04957, 9, 32.12, -27.98, 0.65982, 4, 17, 35.22, 8.28, 0.63445, 3, 39, -31.8, 0.07407, 7, 38.71, -41.24, 0.03901, 9, 9.47, -40.48, 0.25247, 4, 17, 28.54, -0.25, 0.88356, 3, 28.17, -31.97, 0.02845, 7, 28.69, -45.38, 0.0141, 9, -0.01, -45.71, 0.07389, 1, 17, 6.86, -9.85, 1, 2, 17, -6.37, -6.9, 0.58186, 3, 1.06, -8.99, 0.41814, 2, 17, -16.7, -4.6, 0.0015, 3, -3.65, 0.49, 0.9985, 1, 3, -0.45, 8.36, 1, 2, 3, 8.81, 4.47, 0.96101, 4, 6.77, 11.54, 0.03899, 2, 3, 15.45, 6.84, 0.34309, 4, 7.85, 4.57, 0.65691, 1, 7, 2.9, 0.23, 1, 1, 7, 17.81, 6.39, 1, 2, 17, -2.33, 0.69, 0.77739, 3, 9.5, -7.36, 0.22261, 4, 17, 2.42, 12.49, 0.14114, 3, 21.67, -3.64, 0.84055, 7, 12.25, -21.41, 0.0167, 9, -19.05, -23.75, 0.00161, 5, 17, 7.17, 29.41, 0.03796, 3, 37.81, 3.29, 0.09589, 4, 0.15, -16.72, 0.15564, 7, 24.72, -9.03, 0.69107, 9, -8.04, -10.05, 0.01944], "hull": 13, "edges": [16, 14, 10, 8, 16, 18, 18, 20, 20, 22, 22, 24, 24, 0, 4, 2, 0, 2, 4, 6, 6, 8, 10, 12, 12, 14], "width": 68, "height": 52}}, "Wing2": {"Wing2": {"type": "mesh", "uvs": [0.20457, 0, 0.41269, 0, 0.63469, 0.11651, 0.81969, 0.29933, 0.93994, 0.52786, 1, 0.65736, 0.71332, 1, 0.35719, 1, 0, 0.76401, 0, 0.56341, 0.11669, 0.42883, 0.18607, 0.32219], "triangles": [5, 6, 4, 7, 3, 4, 3, 11, 2, 11, 1, 2, 8, 10, 7, 3, 7, 11, 6, 7, 4, 8, 9, 10, 10, 11, 7, 1, 11, 0], "vertices": [2, 6, -7.17, -11.31, 0.55532, 7, -11.53, -7.05, 0.44468, 1, 7, -6.61, 3.52, 1, 3, 6, 2.65, 13.69, 0.00127, 7, 9.41, 9.77, 0.99804, 9, -25.37, 6.92, 0.00069, 3, 6, 20.37, 25.57, 9e-05, 7, 30.69, 11.29, 0.43417, 9, -4.4, 10.82, 0.56574, 1, 9, 19.84, 9.8, 1, 1, 9, 33.44, 8.8, 1, 2, 8, 48.76, 9.81, 0.90992, 9, 61.48, -17.52, 0.09008, 1, 8, 43.52, -9.43, 1, 1, 8, 15.04, -22.41, 1, 2, 6, 51.05, -17.92, 0.16274, 8, -4.7, -17.03, 0.83726, 2, 6, 36.83, -12.55, 0.79958, 8, -16.23, -7.12, 0.20042, 2, 6, 25.66, -9.59, 0.99774, 8, -25.7, -0.51, 0.00226], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 56, "height": 102}}, "Wing3": {"Wing2": {"type": "mesh", "color": "ff6c21ff", "uvs": [0.20457, 0, 0.41269, 0, 0.63469, 0.11651, 0.81969, 0.29933, 0.93994, 0.52786, 1, 0.65736, 0.71332, 1, 0.35719, 1, 0, 0.76401, 0, 0.56341, 0.11669, 0.42883, 0.18607, 0.32219], "triangles": [7, 3, 4, 5, 6, 4, 11, 1, 2, 3, 11, 2, 6, 7, 4, 8, 10, 7, 8, 9, 10, 3, 7, 11, 1, 11, 0, 10, 11, 7], "vertices": [1, 22, -3.28, -6.94, 1, 2, 22, -3.2, 3.71, 0.06086, 55, -3.35, 3.69, 0.93914, 1, 55, 10.34, 11.4, 1, 1, 55, 29.4, 15.48, 1, 1, 55, 51.56, 15.16, 1, 2, 23, 28.32, 21.63, 0.08612, 55, 64, 14.59, 0.91388, 1, 23, 47.58, -7.76, 1, 2, 22, 89.97, 0.14, 0.00051, 23, 37.9, -23.19, 0.99949, 2, 22, 67.83, -17.96, 0.18528, 23, 9.55, -26.99, 0.81472, 2, 22, 49.14, -17.81, 0.69359, 23, -6.28, -17.05, 0.30641, 2, 22, 36.65, -11.75, 0.99819, 23, -13.73, -5.33, 0.00181, 1, 22, 26.74, -8.12, 1], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 56, "height": 102}}, "Wing4": {"Wing4": {"type": "mesh", "uvs": [0.22118, 0, 0.63239, 0, 1, 0.32822, 1, 0.78356, 0.82352, 1, 0, 1, 0, 0.80993, 0.12852, 0.45284], "triangles": [4, 6, 7, 7, 3, 4, 7, 0, 1, 2, 3, 7, 4, 5, 6, 2, 7, 1], "vertices": [1, 55, -5.97, -6.27, 1, 1, 55, -6.39, 3.59, 1, 1, 55, 12.25, 13.23, 1, 1, 55, 38.64, 14.36, 1, 1, 55, 51.36, 10.67, 1, 1, 55, 52.21, -9.08, 1, 1, 55, 41.2, -9.55, 1, 1, 55, 20.37, -7.36, 1], "hull": 8, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 0, 14], "width": 24, "height": 58}}, "Wing5": {"Wing2": {"type": "mesh", "uvs": [0.20457, 0, 0.41269, 0, 0.63469, 0.11651, 0.81969, 0.29933, 0.93994, 0.52786, 1, 0.65736, 0.71332, 1, 0.35719, 1, 0, 0.76401, 0, 0.56341, 0.11669, 0.42883, 0.18607, 0.32219], "triangles": [7, 3, 4, 5, 6, 4, 11, 1, 2, 3, 11, 2, 8, 9, 10, 6, 7, 4, 3, 7, 11, 8, 10, 7, 1, 11, 0, 10, 11, 7], "vertices": [2, 6, -7.17, -11.31, 0.55532, 7, -11.53, -7.05, 0.44468, 1, 7, -6.61, 3.52, 1, 3, 6, 2.65, 13.69, 0.00127, 7, 9.41, 9.77, 0.99804, 9, -25.37, 6.92, 0.00069, 3, 6, 20.37, 25.57, 9e-05, 7, 30.69, 11.29, 0.43417, 9, -4.4, 10.82, 0.56574, 1, 9, 19.84, 9.8, 1, 1, 9, 33.44, 8.8, 1, 2, 8, 48.76, 9.81, 0.90992, 9, 61.48, -17.52, 0.09008, 1, 8, 43.52, -9.43, 1, 1, 8, 15.04, -22.41, 1, 2, 6, 51.05, -17.92, 0.16274, 8, -4.7, -17.03, 0.83726, 2, 6, 36.83, -12.55, 0.79958, 8, -16.23, -7.12, 0.20042, 2, 6, 25.66, -9.59, 0.99774, 8, -25.7, -0.51, 0.00226], "hull": 12, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 0, 22], "width": 56, "height": 102}}}}, "animations": {"animation": {"slots": {"Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"color": [{"time": 1.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.1333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.2667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.7, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.7667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.8333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.9, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.9667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0": {"color": [{"time": 1.7333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.3667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.3667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.4333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.5, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.5667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.6333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1": {"color": [{"time": 1.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.1333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.2667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.7, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.7667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.8333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.9, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.9667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}]}, "Fire/Fire 01": {"attachment": [{"time": 0.3333, "name": "Fire/Fire 01"}, {"time": 0.4, "name": "Fire/Fire 03"}, {"time": 0.4667, "name": "Fire/Fire 05"}, {"time": 0.5333, "name": "Fire/Fire 07"}, {"time": 0.6, "name": "Fire/Fire 09"}, {"time": 0.6667, "name": "Fire/Fire 11"}, {"time": 0.7333, "name": "Fire/Fire 13"}, {"time": 0.8, "name": "Fire/Fire 15"}, {"time": 0.8667, "name": "Fire/Fire 17"}, {"time": 0.9333, "name": "Fire/Fire 19"}, {"time": 1, "name": null}]}, "Fire/Fire 1": {"attachment": [{"time": 0.6667, "name": "Fire/Fire 01"}, {"time": 0.7667, "name": "Fire/Fire 03"}, {"time": 0.8667, "name": "Fire/Fire 05"}, {"time": 0.9667, "name": "Fire/Fire 07"}, {"time": 1.0667, "name": "Fire/Fire 09"}, {"time": 1.1333, "name": "Fire/Fire 11"}, {"time": 1.2333, "name": "Fire/Fire 13"}, {"time": 1.3, "name": "Fire/Fire 15"}, {"time": 1.3667, "name": "Fire/Fire 17"}, {"time": 1.4333, "name": "Fire/Fire 19"}, {"time": 1.5, "name": null}]}, "Sparkle": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 0.9333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 0.9333, "name": "Sparkle"}]}, "Sparkle4": {"color": [{"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "Sparkle"}]}, "Sparkle5": {"color": [{"time": 0.8, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "color": "ffffff00"}], "attachment": [{"time": 0.8, "name": "Sparkle"}]}}, "bones": {"Body": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 11.08, "y": -6.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.56, "y": 6.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 2.6, "y": 8.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.14, "y": 1.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "3D": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -147.27, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 62.07, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "3D_Face": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -61.21, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 150.68, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -31.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -29.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -0.64, "y": -4.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Mouth": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 8.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 27.24, "curve": [0.256, 0, 0.62, 0.46]}, {"time": 1.0667, "angle": 18.51, "curve": [0.366, 0.46, 0.754, 1]}, {"time": 1.6667, "angle": 32.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -6.79, "y": -2.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 4.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 11.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -8.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -5.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -4.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -3.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4667, "x": -17.94, "y": 3.34}, {"time": 0.8, "x": 0, "y": 0}]}, "Wing1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 46.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 16.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 40.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 17.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 6, "y": -1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 3.81, "y": -1.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": -3.8, "y": -0.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Wing2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 11.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 39.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 52.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 55.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.302, 0, 0.638, 0.36]}, {"time": 0.1, "angle": -2.41, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 0.3667, "angle": 71.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -97.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 23.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -105.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.302, 0, 0.638, 0.36]}, {"time": 0.0667, "angle": -4.86, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 0.3333, "angle": 24.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -76.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -14.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -84.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -19.97, "curve": [0.244, 0, 0.697, 0.78]}, {"time": 0.4, "angle": -3.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -17.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -22.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -25.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 44.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -11.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -4.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -7.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 27.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 12.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 36.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 22.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 8.84, "y": -5.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Wing3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 78.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -43.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 51.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -53.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.261, 0, 0.618, 0.44]}, {"time": 0.1667, "angle": -36.58, "curve": [0.357, 0.42, 0.756, 1]}, {"time": 0.4667, "angle": -0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -28.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 10.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -4.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": -10.15, "y": -3.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Wing12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 78.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -24.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 73.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -32.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 14.98, "y": -3.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Tail4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 7.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 7.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -4.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -4.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -4.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -4.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 9.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 24.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 27.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 1.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 8.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail15": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 8.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 8.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0.46, "y": -7.92, "curve": "stepped"}, {"time": 1.4333, "x": 0.46, "y": -7.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Tail6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -0.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -13.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -0.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -10.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -0.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -13.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -0.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail16": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 12.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -4.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail17": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 12.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -4.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail18": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -8.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail19": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": -8.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Star": {"rotate": [{"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 108.76}], "translate": [{"time": 0.6667, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.6667, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1.278, "y": 1.278}]}, "Star2": {"rotate": [{"time": 0.4667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 108.76}], "translate": [{"time": 0.4667, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.4667, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.278, "y": 1.278}]}, "Star3": {"rotate": [{"time": 0.9333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 108.76}], "translate": [{"time": 0.9333, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 18.21, "y": 67.71}], "scale": [{"time": 0.9333, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1.278, "y": 1.278}]}, "Star4": {"rotate": [{"time": 1.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 108.76}], "translate": [{"time": 1.1333, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 18.21, "y": 60.32}], "scale": [{"time": 1.1333, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.393, "y": 1.393}]}, "Star5": {"rotate": [{"time": 0.8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 108.76}], "translate": [{"time": 0.8, "x": 18.21, "y": 7.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.8, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1.278, "y": 1.278}]}, "Wing9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 1.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 18.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 4.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"rotate": [{"time": 0, "angle": -20.95, "curve": "stepped"}, {"time": 0.6667, "angle": -20.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -46.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -15.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": -54.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -37.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -20.95}], "translate": [{"time": 0, "x": 42.22, "y": 9.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 45.92, "y": 16.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 43.89, "y": -6.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 40.93, "y": 18.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 42.94, "y": 11.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 43.87, "y": -7.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 42.22, "y": 9.59}], "scale": [{"time": 0, "x": 0.885, "y": 0.885, "curve": "stepped"}, {"time": 0.4667, "x": 0.885, "y": 0.885, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.608, "y": 1.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.005, "y": 0.943, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.802, "y": 0.753, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1.355, "y": 1.273, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 1.194, "y": 1.121, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.885, "y": 0.885}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0": {"rotate": [{"time": 0, "angle": 12.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 55.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 51.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -96.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -52.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -24.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -56.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -31.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 12.06}], "translate": [{"time": 0, "x": 9.67, "y": -4.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": -5.9, "y": 14.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": -14.94, "y": 7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -8.5, "y": -5.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 9.67, "y": -4.4}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1.534, "y": 1.534, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 2.085, "y": 2.085, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.878, "y": 1.878}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1": {"rotate": [{"time": 0, "angle": -98.25}, {"time": 0.6667, "angle": -114.38}, {"time": 1.2, "angle": -93.41}, {"time": 1.5667, "angle": -114.38}, {"time": 2, "angle": -98.25}], "translate": [{"time": 0, "x": 41.66, "y": 20.69}, {"time": 0.6667, "x": 48.4, "y": 9.76}, {"time": 1.2, "x": 49.86, "y": 25.07}, {"time": 1.5667, "x": 49.15, "y": 7.48}, {"time": 2, "x": 41.66, "y": 20.69}], "scale": [{"time": 0, "x": 0.547, "y": 0.547}, {"time": 0.6667, "x": 0.664, "y": 0.664, "curve": "stepped"}, {"time": 1.5667, "x": 0.664, "y": 0.664}, {"time": 2, "x": 0.547, "y": 0.547}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 168, "vertices": [0.29408, 0.88341, -0.90089, 0.23532, 0.93055, 0.02912, 0.00436, 1.71196, 0.4798, -1.64321, -0.19989, -0.81277, 0.82415, -0.14589, -0.83182, -0.09314, -0.93044, -0.03577, 0.09694, -0.926, -0.35776, -0.8597, -0.35499, 0.8608], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 175, "vertices": [-0.63895, -0.18458, 0.6117, -0.13091, 0.48786, -0.47816, -0.16277, 0.41262, 0.29135, 0.46928, 0.35601, -0.38614, 0.44479, -0.12642, 0.57531, 0.49619, -0.31746], "curve": "stepped"}, {"time": 1.6667, "offset": 175, "vertices": [-0.63895, -0.18458, 0.6117, -0.13091, 0.48786, -0.47816, -0.16277, 0.41262, 0.29135, 0.46928, 0.35601, -0.38614, 0.44479, -0.12642, 0.57531, 0.49619, -0.31746], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}, "without_frame": {"slots": {"BG": {"attachment": [{"time": 0, "name": null}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"color": [{"time": 1.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.1333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.2667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.7, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.7667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.8333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.9, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.9667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0": {"color": [{"time": 1.7333, "color": "ffffffff"}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.3667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.3667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.4333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.5, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.5667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.6333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1": {"color": [{"time": 1.7333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "color": "ffffff00"}], "attachment": [{"time": 0.0667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.1333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.2, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.2667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 0.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 0.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 0.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 0.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 0.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 0.7, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 0.7667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 0.8333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 0.9, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 0.9667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.0333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.1, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.1667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.2333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.3, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}, {"time": 1.3333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000"}, {"time": 1.4, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00002"}, {"time": 1.4667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00004"}, {"time": 1.5333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00006"}, {"time": 1.6, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00008"}, {"time": 1.6667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00010"}, {"time": 1.7333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00012"}, {"time": 1.8, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00014"}, {"time": 1.8667, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00016"}, {"time": 1.9333, "name": "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00018"}]}, "Fire/Fire 01": {"attachment": [{"time": 0.3333, "name": "Fire/Fire 01"}, {"time": 0.4, "name": "Fire/Fire 03"}, {"time": 0.4667, "name": "Fire/Fire 05"}, {"time": 0.5333, "name": "Fire/Fire 07"}, {"time": 0.6, "name": "Fire/Fire 09"}, {"time": 0.6667, "name": "Fire/Fire 11"}, {"time": 0.7333, "name": "Fire/Fire 13"}, {"time": 0.8, "name": "Fire/Fire 15"}, {"time": 0.8667, "name": "Fire/Fire 17"}, {"time": 0.9333, "name": "Fire/Fire 19"}, {"time": 1, "name": null}]}, "Fire/Fire 1": {"attachment": [{"time": 0.6667, "name": "Fire/Fire 01"}, {"time": 0.7667, "name": "Fire/Fire 03"}, {"time": 0.8667, "name": "Fire/Fire 05"}, {"time": 0.9667, "name": "Fire/Fire 07"}, {"time": 1.0667, "name": "Fire/Fire 09"}, {"time": 1.1333, "name": "Fire/Fire 11"}, {"time": 1.2333, "name": "Fire/Fire 13"}, {"time": 1.3, "name": "Fire/Fire 15"}, {"time": 1.3667, "name": "Fire/Fire 17"}, {"time": 1.4333, "name": "Fire/Fire 19"}, {"time": 1.5, "name": null}]}, "Sparkle": {"color": [{"time": 0.6667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "color": "ffffff00"}], "attachment": [{"time": 0.6667, "name": "Sparkle"}]}, "Sparkle2": {"color": [{"time": 0.4667, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "color": "ffffff00"}], "attachment": [{"time": 0.4667, "name": "Sparkle"}]}, "Sparkle3": {"color": [{"time": 0.9333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "color": "ffffff00"}], "attachment": [{"time": 0.9333, "name": "Sparkle"}]}, "Sparkle4": {"color": [{"time": 1.1333, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "color": "ffffff00"}], "attachment": [{"time": 1.1333, "name": "Sparkle"}]}, "Sparkle5": {"color": [{"time": 0.8, "color": "ffffff00", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "color": "ffffffff", "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "color": "ffffff00"}], "attachment": [{"time": 0.8, "name": "Sparkle"}]}}, "bones": {"Body": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -3.09, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -6.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 11.08, "y": -6.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 0.56, "y": 6.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 2.6, "y": 8.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 0.9, "y": 0.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "x": 1.12, "y": 1.12, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.14, "y": 1.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 1, "y": 1}]}, "3D": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -147.27, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 62.07, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "3D_Face": {"translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": -61.21, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 150.68, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Head": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -31.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -29.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.6667, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -0.64, "y": -4.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Mouth": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 8.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": -3.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": 27.24, "curve": [0.256, 0, 0.62, 0.46]}, {"time": 1.0667, "angle": 18.51, "curve": [0.366, 0.46, 0.754, 1]}, {"time": 1.6667, "angle": 32.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.8, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": -6.79, "y": -2.36, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Body5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": 4.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": 11.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -8.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -5.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -5.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 6.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 4.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 1.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -4.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Body4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -3.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": 6.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -0.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 3.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -1.24, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "angle": 2.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0}, {"time": 0.4667, "x": -17.94, "y": 3.34}, {"time": 0.8, "x": 0, "y": 0}]}, "Wing1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 46.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": 16.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 40.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": 17.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "x": 6, "y": -1.18, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "x": 3.81, "y": -1.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "x": -3.8, "y": -0.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Wing2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3, "angle": 11.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": 39.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1333, "angle": 52.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": 55.62, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.302, 0, 0.638, 0.36]}, {"time": 0.1, "angle": -2.41, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 0.3667, "angle": 71.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -97.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": 23.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -105.29, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.302, 0, 0.638, 0.36]}, {"time": 0.0667, "angle": -4.86, "curve": [0.293, 0.18, 0.755, 1]}, {"time": 0.3333, "angle": 24.33, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -76.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": -14.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -84.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -19.97, "curve": [0.244, 0, 0.697, 0.78]}, {"time": 0.4, "angle": -3.82, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -17.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -22.37, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -25.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 44.74, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -11.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2, "angle": -4.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -7.02, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 27.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": 12.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 36.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 22.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0.3333, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 8.84, "y": -5.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Wing3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 78.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -43.98, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 51.31, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -53.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Wing11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.261, 0, 0.618, 0.44]}, {"time": 0.1667, "angle": -36.58, "curve": [0.357, 0.42, 0.756, 1]}, {"time": 0.4667, "angle": -0.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -28.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 10.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": -4.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "x": -10.15, "y": -3.23, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Wing12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.0667, "angle": -1.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 78.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -24.48, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 73.25, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -32.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Head4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8, "angle": -13.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 4.72, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4667, "angle": -4.67, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 3.86, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail1": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 3.73, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "x": 14.98, "y": -3.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 0, "y": 0}]}, "Tail4": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 7.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail3": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -7.5, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 2.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -4.41, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 8.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -2.07, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": 7.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail2": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 11.55, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1, "angle": 3.32, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": -0.7, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 5.75, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -4.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail10": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": -4.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 6.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -1.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": 8.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail11": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -4.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail12": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1, "angle": -9.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 6.56, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -4.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 4.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": -4.22, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 5.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail13": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "angle": 9.85, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": 3.65, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 24.83, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3667, "angle": 14.97, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 27.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail14": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1333, "angle": -5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "angle": 8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": 1.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 8.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5667, "angle": -2.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail15": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -5.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 8.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": 1.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 8.61, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6333, "angle": 8.96, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail5": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "angle": -7.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4333, "angle": -2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}], "translate": [{"time": 0, "x": 0, "y": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7333, "x": 0.46, "y": -7.92, "curve": "stepped"}, {"time": 1.4333, "x": 0.46, "y": -7.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0, "y": 0}]}, "Tail6": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 8.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -7.76, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 5.01, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": -2.6, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": 1.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail7": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": -10.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "angle": -0.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8333, "angle": -13.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -0.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail8": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -10.77, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5333, "angle": -0.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -13.42, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": 0.35, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6, "angle": -12.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8667, "angle": -0.1, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail16": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 12.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -4.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail17": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4333, "angle": 12.3, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7667, "angle": -4.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1, "angle": 6.44, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -1.53, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail18": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9333, "angle": -13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -8.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Tail19": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2333, "angle": -13.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6333, "angle": -0.81, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9667, "angle": -13.8, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 2.43, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": -8.27, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Star": {"rotate": [{"time": 0.6667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "angle": 108.76}], "translate": [{"time": 0.6667, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.6667, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2667, "x": 1.278, "y": 1.278}]}, "Star2": {"rotate": [{"time": 0.4667, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": 108.76}], "translate": [{"time": 0.4667, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.4667, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 1.278, "y": 1.278}]}, "Star3": {"rotate": [{"time": 0.9333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": 108.76}], "translate": [{"time": 0.9333, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 18.21, "y": 67.71}], "scale": [{"time": 0.9333, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1.278, "y": 1.278}]}, "Star4": {"rotate": [{"time": 1.1333, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "angle": 108.76}], "translate": [{"time": 1.1333, "x": 18.21, "y": 24.47, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 18.21, "y": 60.32}], "scale": [{"time": 1.1333, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.6667, "x": 1.393, "y": 1.393}]}, "Star5": {"rotate": [{"time": 0.8, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "angle": 108.76}], "translate": [{"time": 0.8, "x": 18.21, "y": 7.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 18.21, "y": 50.6}], "scale": [{"time": 0.8, "x": 0.819, "y": 0.819, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.4, "x": 1.278, "y": 1.278}]}, "Wing9": {"rotate": [{"time": 0, "angle": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.2667, "angle": 4.57, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5667, "angle": 1.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0667, "angle": 18.08, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -15.28, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7667, "angle": 4.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 0}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_00000": {"rotate": [{"time": 0, "angle": -20.95, "curve": "stepped"}, {"time": 0.6667, "angle": -20.95, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.9, "angle": -46.92, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "angle": 15.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "angle": 4.66, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "angle": -15.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7, "angle": -54.9, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8333, "angle": -37.58, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": -20.95}], "translate": [{"time": 0, "x": 42.22, "y": 9.59, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.4667, "x": 45.92, "y": 16.54, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.6667, "x": 43.89, "y": -6.79, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 40.93, "y": 18.34, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 42.94, "y": 11.26, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 43.87, "y": -7.91, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 42.22, "y": 9.59}], "scale": [{"time": 0, "x": 0.885, "y": 0.885, "curve": "stepped"}, {"time": 0.4667, "x": 0.885, "y": 0.885, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.8667, "x": 1.608, "y": 1.51, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.1667, "x": 1.005, "y": 0.943, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.3333, "x": 0.802, "y": 0.753, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5333, "x": 1.355, "y": 1.273, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.8, "x": 1.194, "y": 1.121, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 0.885, "y": 0.885}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_0": {"rotate": [{"time": 0, "angle": 12.06, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.1667, "angle": 55.46, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3667, "angle": 51.21, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "angle": -96.63, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "angle": -52.99, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.2333, "angle": -24.69, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "angle": -56.14, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.7333, "angle": -31.93, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "angle": 12.06}], "translate": [{"time": 0, "x": 9.67, "y": -4.4, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": -5.9, "y": 14.88, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": -14.94, "y": 7.03, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": -8.5, "y": -5.45, "curve": [0.25, 0, 0.75, 1]}, {"time": 2, "x": 9.67, "y": -4.4}], "scale": [{"time": 0, "x": 1, "y": 1, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.7, "x": 1.534, "y": 1.534, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.0333, "x": 2.085, "y": 2.085, "curve": [0.25, 0, 0.75, 1]}, {"time": 1.5, "x": 1.878, "y": 1.878}]}, "Fire/Elements - Fire 112 Torch Calm loop noCT noRSZ/Elements - Fire 112 Torch Calm loop noCT noRSZ_1": {"rotate": [{"time": 0, "angle": -98.25}, {"time": 0.6667, "angle": -114.38}, {"time": 1.2, "angle": -93.41}, {"time": 1.5667, "angle": -114.38}, {"time": 2, "angle": -98.25}], "translate": [{"time": 0, "x": 41.66, "y": 20.69}, {"time": 0.6667, "x": 48.4, "y": 9.76}, {"time": 1.2, "x": 49.86, "y": 25.07}, {"time": 1.5667, "x": 49.15, "y": 7.48}, {"time": 2, "x": 41.66, "y": 20.69}], "scale": [{"time": 0, "x": 0.547, "y": 0.547}, {"time": 0.6667, "x": 0.664, "y": 0.664, "curve": "stepped"}, {"time": 1.5667, "x": 0.664, "y": 0.664}, {"time": 2, "x": 0.547, "y": 0.547}]}, "root": {"translate": [{"time": 0, "x": 0, "y": -4}]}, "All": {"translate": [{"time": 0, "x": 2.46, "y": 5.4}]}}, "deform": {"default": {"Head": {"Head": [{"time": 0, "curve": [0.25, 0, 0.75, 1]}, {"time": 0.3333, "offset": 168, "vertices": [0.29408, 0.88341, -0.90089, 0.23532, 0.93055, 0.02912, 0.00436, 1.71196, 0.4798, -1.64321, -0.19989, -0.81277, 0.82415, -0.14589, -0.83182, -0.09314, -0.93044, -0.03577, 0.09694, -0.926, -0.35776, -0.8597, -0.35499, 0.8608], "curve": [0.25, 0, 0.75, 1]}, {"time": 0.5, "offset": 175, "vertices": [-0.63895, -0.18458, 0.6117, -0.13091, 0.48786, -0.47816, -0.16277, 0.41262, 0.29135, 0.46928, 0.35601, -0.38614, 0.44479, -0.12642, 0.57531, 0.49619, -0.31746], "curve": "stepped"}, {"time": 1.6667, "offset": 175, "vertices": [-0.63895, -0.18458, 0.6117, -0.13091, 0.48786, -0.47816, -0.16277, 0.41262, 0.29135, 0.46928, 0.35601, -0.38614, 0.44479, -0.12642, 0.57531, 0.49619, -0.31746], "curve": [0.25, 0, 0.75, 1]}, {"time": 2}]}}}}}}