[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "aquariumLeaderboardView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 9}, {"__id__": 12}, {"__id__": 17}, {"__id__": 22}, {"__id__": 29}, {"__id__": 32}, {"__id__": 35}, {"__id__": 38}, {"__id__": 41}, {"__id__": 44}, {"__id__": 47}, {"__id__": 50}, {"__id__": 53}, {"__id__": 56}, {"__id__": 100}], "_active": true, "_level": 2, "_components": [{"__id__": 144}, {"__id__": 145}], "_prefab": {"__id__": 146}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "d6pzTHVSpLlKnpljVD1+D0", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 955, "height": 594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4b8b6720-d94b-4767-88f2-d4145f023b60"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "d8nq8iaN5HBb24u9Y5CxJS", "sync": false}, {"__type__": "cc.Node", "_name": "title_bxh", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 249, "height": 48}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 293, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "edaa2ca9-aa1f-4697-8cf6-c1d2fbaa9b75"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "9dC79Ykw5A3aStPhP33mTO", "sync": false}, {"__type__": "cc.Node", "_name": "btnJackpot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 13}, {"__id__": 14}], "_prefab": {"__id__": 16}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-85, 219.8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2c0ad6dd-cb22-41fa-a773-e681b9a66f59"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 15}], "_N$interactable": false, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$pressedSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "pressedSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$hoverSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "hoverSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$disabledSprite": {"__uuid__": "2c0ad6dd-cb22-41fa-a773-e681b9a66f59"}, "_N$target": {"__id__": 12}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "3c6a1IcT7pG8b7cqDRMjmCv", "handler": "jackpotTabClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "87kYQ0a7tCIrTph4cDoVQz", "sync": false}, {"__type__": "cc.Node", "_name": "btnBigWin", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 2, "_components": [{"__id__": 18}, {"__id__": 19}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 140, "height": 64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [85, 219.8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 17}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [{"__id__": 20}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 2, "transition": 2, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$pressedSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "pressedSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$hoverSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "hoverSprite": {"__uuid__": "8d05dbe2-6b20-491c-b1f4-4a3ba08374c2"}, "_N$disabledSprite": {"__uuid__": "2c0ad6dd-cb22-41fa-a773-e681b9a66f59"}, "_N$target": {"__id__": 17}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "3c6a1IcT7pG8b7cqDRMjmCv", "handler": "bigWinTabClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "0ftZVSULZJT5R3/wQW/5Zb", "sync": false}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 23}], "_active": true, "_level": 3, "_components": [{"__id__": 26}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [452, 277, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 22}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 24}], "_prefab": {"__id__": 25}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 23}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "44e1e7a6-07b2-4a44-9b94-d08c64afe637"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "0b4Me7kdhEirov/1fUk5h/", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 27}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 22}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "3c6a1IcT7pG8b7cqDRMjmCv", "handler": "backClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "43wpGo6T5OhLNAJJkS0JFl", "sync": false}, {"__type__": "cc.Node", "_name": "spriteBGTitle", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 29}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "2b568c5a-**************-604e61f8d003"}, "_type": 1, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "96QdSHO7pArpKpKrp0Sdtf", "sync": false}, {"__type__": "cc.Node", "_name": "lbJackpot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 33}], "_prefab": {"__id__": 34}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-85, 222.8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 32}, "_enabled": true, "_useOriginalSize": false, "_string": "NỔ HŨ", "_N$string": "NỔ HŨ", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "ccnuX3Pi9MnbtThpu1KGXB", "sync": false}, {"__type__": "cc.Node", "_name": "lbBigWin", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 123, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [85, 222.8, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 35}, "_enabled": true, "_useOriginalSize": false, "_string": "THẮNG LỚN", "_N$string": "THẮNG LỚN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "6ffVcfbW5J17QscAaoKdap", "sync": false}, {"__type__": "cc.Node", "_name": "lb\bSessionID", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 39}], "_prefab": {"__id__": 40}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-361, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_useOriginalSize": false, "_string": "PHIÊN", "_N$string": "PHIÊN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "afb9pP8JJO24NsfJ+t/DD0", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 42}], "_prefab": {"__id__": 43}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-213.9, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 41}, "_enabled": true, "_useOriginalSize": false, "_string": "THỜI GIAN", "_N$string": "THỜI GIAN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "d6yz/Ymo1NV62j80MGC/So", "sync": false}, {"__type__": "cc.Node", "_name": "lbAccount", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 45}], "_prefab": {"__id__": 46}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.4, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 44}, "_enabled": true, "_useOriginalSize": false, "_string": "TÀI KHOẢN", "_N$string": "TÀI KHOẢN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "9a6To8q/1OsLCokM9LfYEO", "sync": false}, {"__type__": "cc.Node", "_name": "lbRoom", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 48}], "_prefab": {"__id__": 49}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [137, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 47}, "_enabled": true, "_useOriginalSize": false, "_string": "PHÒNG", "_N$string": "PHÒNG", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "c1r4Us7kpEk4CmeZgCqq0N", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 51}], "_prefab": {"__id__": 52}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [226.6, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 50}, "_enabled": true, "_useOriginalSize": false, "_string": "THẮNG", "_N$string": "THẮNG", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "94QbzezMxN16EDFB52KhZF", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 54}], "_prefab": {"__id__": 55}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [339.6, 158, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 53}, "_enabled": true, "_useOriginalSize": false, "_string": "MÔ TẢ", "_N$string": "MÔ TẢ", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "a2b415f3-8e64-4758-87ad-4b5ce4c6e882"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "d2MBbGM2VOtoA8FOpAff/O", "sync": false}, {"__type__": "cc.Node", "_name": "lbJackpotView", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 57}], "_active": false, "_level": 2, "_components": [{"__id__": 98}], "_prefab": {"__id__": 99}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 33, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg<PERSON><PERSON>nt", "_objFlags": 0, "_parent": {"__id__": 56}, "_children": [{"__id__": 58}], "_active": true, "_level": 3, "_components": [], "_prefab": {"__id__": 97}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -82, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 57}, "_children": [{"__id__": 59}, {"__id__": 88}], "_active": true, "_level": 3, "_components": [{"__id__": 94}, {"__id__": 95}], "_prefab": {"__id__": 96}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 60}], "_active": true, "_level": 5, "_components": [], "_prefab": {"__id__": 87}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 59}, "_children": [{"__id__": 61}, {"__id__": 64}, {"__id__": 67}, {"__id__": 76}, {"__id__": 79}, {"__id__": 82}], "_active": true, "_level": 5, "_components": [{"__id__": 85}], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 114, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lb\bSessionID", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 62}], "_prefab": {"__id__": 63}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-361, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 61}, "_enabled": true, "_useOriginalSize": false, "_string": "#66563620", "_N$string": "#66563620", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "cflaz4u61B6K0gfJ3cGRSk", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 65}], "_prefab": {"__id__": 66}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-213.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 64}, "_enabled": true, "_useOriginalSize": false, "_string": "20:45 21-08-2018", "_N$string": "20:45 21-08-2018", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "51Vvdt9OpMPI0A5s2rsP75", "sync": false}, {"__type__": "cc.Node", "_name": "layout-nick<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [{"__id__": 68}, {"__id__": 71}], "_active": true, "_level": 6, "_components": [{"__id__": 74}], "_prefab": {"__id__": 75}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.4, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lbSID", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_level": 8, "_components": [{"__id__": 69}], "_prefab": {"__id__": 70}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 45, "g": 196, "b": 12, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 68}, "_enabled": true, "_useOriginalSize": false, "_string": "[TQ]", "_N$string": "[TQ]", "_fontSize": 20, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "756oyRYmVHE5SQff96BYot", "sync": false}, {"__type__": "cc.Node", "_name": "lbNickName", "_objFlags": 0, "_parent": {"__id__": 67}, "_children": [], "_active": true, "_level": 8, "_components": [{"__id__": 72}], "_prefab": {"__id__": 73}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 71}, "_enabled": true, "_useOriginalSize": false, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 20, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "03AjP0kjpOJLXzfRJ5TPPI", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 67}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 137, "height": 50}, "_resize": 1, "_N$layoutType": 1, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 5, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8bgFgCV8lIXp0cmJQiqJJm", "sync": false}, {"__type__": "cc.Node", "_name": "lbRoom", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 77}], "_prefab": {"__id__": 78}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [137, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 76}, "_enabled": true, "_useOriginalSize": false, "_string": "10.000", "_N$string": "10.000", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "29SSUjp8BKqatpKoQNZVzg", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 80}], "_prefab": {"__id__": 81}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [226.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 79}, "_enabled": true, "_useOriginalSize": false, "_string": "50.000.000", "_N$string": "50.000.000", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "af7N3969BOm6gUSwTI9zud", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 60}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 83}], "_prefab": {"__id__": 84}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 219, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 82}, "_enabled": true, "_useOriginalSize": false, "_string": "THẮNG LỚN", "_N$string": "THẮNG LỚN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "5cLpq3ZxhDWoko0FUC0L/Z", "sync": false}, {"__type__": "6c6718pco1KcbJmkeGVCmhO", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "lbSessionID": {"__id__": 62}, "lbTime": {"__id__": 65}, "lbSID": {"__id__": 69}, "lbNickName": {"__id__": 72}, "lbRoom": {"__id__": 77}, "lbWin": {"__id__": 80}, "lbDesc": {"__id__": 83}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8a+KNa4C1Bj4z6vCpjbkkI", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8fh427WmNFfoZ7362KP/9R", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 58}, "_children": [{"__id__": 89}], "_active": true, "_level": 0, "_components": [{"__id__": 92}], "_prefab": {"__id__": 93}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 88}, "_children": [], "_active": true, "_level": 0, "_components": [{"__id__": 90}], "_prefab": {"__id__": 91}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 184, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 89}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "f9i7jSJQVHWKbOSVNXEB4q", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 88}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "f80XmKEd5MbJKZCSjnwQ/r", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 89}, "content": {"__id__": 89}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "088def4kH5OCoVfmOqRAxDh", "_name": "", "_objFlags": 0, "node": {"__id__": 58}, "_enabled": true, "itemTemplate": {"__id__": 60}, "scrollView": {"__id__": 94}, "spawnCount": 15, "spacing": 10, "bufferZone": 400, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "a8VnlY6/FId5gRuaywSmYP", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "ff5l900zFCMbAJUoRUlDSv", "sync": false}, {"__type__": "5f63a+KMdxKmINJ7inujvgg", "_name": "", "_objFlags": 0, "node": {"__id__": 56}, "_enabled": true, "slotsLBJackpotListView": {"__id__": 95}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "24HSkW/SZDXpPc435hcYxn", "sync": false}, {"__type__": "cc.Node", "_name": "lbBig<PERSON>in<PERSON>iew", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 101}], "_active": false, "_level": 2, "_components": [{"__id__": 142}], "_prefab": {"__id__": 143}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 33, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "bg<PERSON><PERSON>nt", "_objFlags": 0, "_parent": {"__id__": 100}, "_children": [{"__id__": 102}], "_active": true, "_level": 3, "_components": [], "_prefab": {"__id__": 141}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -82, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "scrollview", "_objFlags": 0, "_parent": {"__id__": 101}, "_children": [{"__id__": 103}, {"__id__": 132}], "_active": true, "_level": 3, "_components": [{"__id__": 138}, {"__id__": 139}], "_prefab": {"__id__": 140}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "temp", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [{"__id__": 104}], "_active": true, "_level": 5, "_components": [], "_prefab": {"__id__": 131}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 2000, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 103}, "_children": [{"__id__": 105}, {"__id__": 108}, {"__id__": 111}, {"__id__": 120}, {"__id__": 123}, {"__id__": 126}], "_active": true, "_level": 5, "_components": [{"__id__": 129}], "_prefab": {"__id__": 130}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lb\bSessionID", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 106}], "_prefab": {"__id__": 107}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-361, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 105}, "_enabled": true, "_useOriginalSize": false, "_string": "PHIÊN", "_N$string": "PHIÊN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8dhmJVSL1ImYSCL4/m8ok1", "sync": false}, {"__type__": "cc.Node", "_name": "lbTime", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 109}], "_prefab": {"__id__": 110}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 220, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-213.9, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 108}, "_enabled": true, "_useOriginalSize": false, "_string": "THỜI GIAN", "_N$string": "THỜI GIAN", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8eFqCKeYxNnK70y3nKBZrd", "sync": false}, {"__type__": "cc.Node", "_name": "layout-nick<PERSON><PERSON>", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [{"__id__": 112}, {"__id__": 115}], "_active": true, "_level": 6, "_components": [{"__id__": 118}], "_prefab": {"__id__": 119}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 137, "height": 50}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-14.4, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "lbSID", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_level": 8, "_components": [{"__id__": 113}], "_prefab": {"__id__": 114}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 45, "g": 196, "b": 12, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 37, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-50, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 112}, "_enabled": true, "_useOriginalSize": false, "_string": "[TQ]", "_N$string": "[TQ]", "_fontSize": 20, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "1fR81LON5IqZHHa2DAmkjH", "sync": false}, {"__type__": "cc.Node", "_name": "lbNickName", "_objFlags": 0, "_parent": {"__id__": 111}, "_children": [], "_active": true, "_level": 8, "_components": [{"__id__": 116}], "_prefab": {"__id__": 117}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 95, "height": 25}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [21, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 115}, "_enabled": true, "_useOriginalSize": false, "_string": "<PERSON><PERSON><PERSON>", "_N$string": "<PERSON><PERSON><PERSON>", "_fontSize": 20, "_lineHeight": 50, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "95SiXECvlJFZMOrWJlaoNr", "sync": false}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 111}, "_enabled": true, "_layoutSize": {"__type__": "cc.Size", "width": 137, "height": 50}, "_resize": 1, "_N$layoutType": 1, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 5, "_N$spacingY": 0, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "6dnjKrQ5VMwJTBSBl6DVNS", "sync": false}, {"__type__": "cc.Node", "_name": "lbRoom", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 121}], "_prefab": {"__id__": 122}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [137, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 120}, "_enabled": true, "_useOriginalSize": false, "_string": "PHÒNG", "_N$string": "PHÒNG", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "ffmp7cXJRO7bBOJL593Bht", "sync": false}, {"__type__": "cc.Node", "_name": "lbWin", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 124}], "_prefab": {"__id__": 125}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [226.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 123}, "_enabled": true, "_useOriginalSize": false, "_string": "THẮNG", "_N$string": "THẮNG", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "24viS3gkRFe6nZv0V5L4+1", "sync": false}, {"__type__": "cc.Node", "_name": "lbDesc", "_objFlags": 0, "_parent": {"__id__": 104}, "_children": [], "_active": true, "_level": 6, "_components": [{"__id__": 127}], "_prefab": {"__id__": 128}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 0, "g": 230, "b": 253, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 30}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [339.6, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 126}, "_enabled": true, "_useOriginalSize": false, "_string": "MÔ TẢ", "_N$string": "MÔ TẢ", "_fontSize": 20, "_lineHeight": 30, "_enableWrapText": false, "_N$file": {"__uuid__": "01ec99f7-66fd-45b7-b872-81d8e9692a0a"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 1, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "24qgBaX9hKHaP3/XNt7Foo", "sync": false}, {"__type__": "3566aJpBw5KOJAxbKKG26cU", "_name": "", "_objFlags": 0, "node": {"__id__": 104}, "_enabled": true, "lbSessionID": {"__id__": 106}, "lbTime": {"__id__": 109}, "lbSID": {"__id__": 113}, "lbNickName": {"__id__": 116}, "lbRoom": {"__id__": 121}, "lbWin": {"__id__": 124}, "lbDesc": {"__id__": 127}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "e83D/kf1pErK/CjP17hBXL", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "25s8eLPQ1NWa9T4CdSx7iV", "sync": false}, {"__type__": "cc.Node", "_name": "view", "_objFlags": 0, "_parent": {"__id__": 102}, "_children": [{"__id__": 133}], "_active": true, "_level": 0, "_components": [{"__id__": 136}], "_prefab": {"__id__": 137}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 370}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "content", "_objFlags": 0, "_parent": {"__id__": 132}, "_children": [], "_active": true, "_level": 0, "_components": [{"__id__": 134}], "_prefab": {"__id__": 135}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 840, "height": 75}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 184, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "node": {"__id__": 133}, "_enabled": false, "_layoutSize": {"__type__": "cc.Size", "width": 1190, "height": 75}, "_resize": 1, "_N$layoutType": 2, "_N$padding": 0, "_N$cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_N$startAxis": 0, "_N$paddingLeft": 0, "_N$paddingRight": 0, "_N$paddingTop": 0, "_N$paddingBottom": 0, "_N$spacingX": 0, "_N$spacingY": 10, "_N$verticalDirection": 1, "_N$horizontalDirection": 0, "_N$affectedByScale": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "518iqyYuVMW6rldWOsVEyG", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 132}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 0.1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "8dME/HpQ5FRZ8wfZuXCx7M", "sync": false}, {"__type__": "<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "horizontal": false, "vertical": true, "inertia": true, "brake": 0.75, "elastic": true, "bounceDuration": 0.23, "scrollEvents": [], "cancelInnerEvents": true, "_N$content": {"__id__": 133}, "content": {"__id__": 133}, "_N$horizontalScrollBar": null, "_N$verticalScrollBar": null, "_id": ""}, {"__type__": "7843dJ1OI1CC4ZbqgEYhC5i", "_name": "", "_objFlags": 0, "node": {"__id__": 102}, "_enabled": true, "itemTemplate": {"__id__": 104}, "scrollView": {"__id__": 138}, "spawnCount": 15, "spacing": 10, "bufferZone": 400, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "3074zqgHlMmqmF/ooc1rUl", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "11xltCuopGxpqVoE6cw/ZB", "sync": false}, {"__type__": "3f026hcsZBBlIzyApf7sacO", "_name": "", "_objFlags": 0, "node": {"__id__": 100}, "_enabled": true, "slotsLBBigWinListView": {"__id__": 139}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "9djQCWVb1IVo8i+p/oh5EU", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "3c6a1IcT7pG8b7cqDRMjmCv", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "btnJackpot": {"__id__": 14}, "btnBigWin": {"__id__": 19}, "nodeJackpot": {"__id__": 56}, "nodeBigWin": {"__id__": 100}, "bigWinView": {"__id__": 142}, "jackpotView": {"__id__": 98}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b051ff90-7657-4daf-a5d8-ea70b39b9bf8"}, "fileId": "9b2uVQ1o9PI7Ah0a6twS7F", "sync": false}]