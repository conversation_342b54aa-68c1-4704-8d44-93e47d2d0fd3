[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false}, {"__type__": "cc.Node", "_name": "aquariumSessionDetailView", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 9}, {"__id__": 12}, {"__id__": 19}, {"__id__": 22}, {"__id__": 25}, {"__id__": 28}, {"__id__": 31}, {"__id__": 34}, {"__id__": 37}], "_active": true, "_level": 2, "_components": [{"__id__": 87}, {"__id__": 88}], "_prefab": {"__id__": 89}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "black", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 100, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 3000, "height": 3000}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 26, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a95690f4-0cfe-4b25-840f-5c84d9abeba3"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "duration": 0.1, "zoomScale": 1.2, "clickEvents": [], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 0, "transition": 0, "_N$normalColor": {"__type__": "cc.Color", "r": 214, "g": 214, "b": 214, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "30FLp1bAZN8Z7dOjXcpM9H", "sync": false}, {"__type__": "cc.Node", "_name": "nen popup", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 955, "height": 594}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 31, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "4b8b6720-d94b-4767-88f2-d4145f023b60"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "13+KrxE41MOY9boehDxIQ+", "sync": false}, {"__type__": "cc.Node", "_name": "title_ctp", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 217, "height": 38}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 296, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "6c90ad1d-c10e-43f4-8cdf-dc06b2e70183"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "f89dhvf19CpbbJnyn+WhSn", "sync": false}, {"__type__": "cc.Node", "_name": "btnClose", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_level": 3, "_components": [{"__id__": 16}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 80, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [452, 277, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "sprite", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_level": 4, "_components": [{"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 59, "height": 61}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "44e1e7a6-07b2-4a44-9b94-d08c64afe637"}, "_type": 0, "_sizeMode": 2, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": false, "_state": 0, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "b8W0ZhkuFKjIgNRlh0GXM3", "sync": false}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "duration": 0.1, "zoomScale": 1.1, "clickEvents": [{"__id__": 17}], "_N$interactable": true, "_N$enableAutoGrayEffect": false, "_N$transition": 3, "transition": 3, "_N$normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "hoverColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$disabledColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_N$normalSprite": null, "_N$pressedSprite": null, "pressedSprite": null, "_N$hoverSprite": null, "hoverSprite": null, "_N$disabledSprite": null, "_N$target": {"__id__": 12}, "_id": ""}, {"__type__": "cc.ClickEvent", "target": {"__id__": 1}, "component": "", "_componentId": "bfff4nSfydEvoywHot7bNxR", "handler": "backClicked", "customEventData": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "cb31BcTLNL5b8CYUYUjA71", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 309.1, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-57, 232, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_useOriginalSize": false, "_string": "THUỶ CUNG - CHI TIẾT PHIÊN:", "_N$string": "THUỶ CUNG - CHI TIẾT PHIÊN:", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 1, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "a2T4/uN7NOM6RzQvmXym+E", "sync": false}, {"__type__": "cc.Node", "_name": "lbSessionID", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 114.95, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [105, 232, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 22}, "_enabled": true, "_useOriginalSize": false, "_string": "#12345678", "_N$string": "#12345678", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "18r0oRAphCQYXqc2krkNbj", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 26}], "_prefab": {"__id__": 27}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 57.75, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-382, 197, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "_useOriginalSize": false, "_string": "Cược:", "_N$string": "Cược:", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "80eezcNbNCA48VJ0g+YwcD", "sync": false}, {"__type__": "cc.Node", "_name": "lbTotalBet", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 29}], "_prefab": {"__id__": 30}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 83.05, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-315, 197, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 28}, "_enabled": true, "_useOriginalSize": false, "_string": "250.000", "_N$string": "250.000", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "4ePEHnnsJMPLK2g/y/uA4W", "sync": false}, {"__type__": "cc.Node", "_name": "label", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 32}], "_prefab": {"__id__": 33}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 68.75, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-382, 169, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 31}, "_enabled": true, "_useOriginalSize": false, "_string": "Thắng:", "_N$string": "Thắng:", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "81y9PfPb5LXqcVP3tlNlns", "sync": false}, {"__type__": "cc.Node", "_name": "lbTotalWin", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_level": 3, "_components": [{"__id__": 35}], "_prefab": {"__id__": 36}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 83.05, "height": 22}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-304, 169, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "node": {"__id__": 34}, "_enabled": true, "_useOriginalSize": false, "_string": "250.000", "_N$string": "250.000", "_fontSize": 22, "_lineHeight": 40, "_enableWrapText": true, "_N$file": {"__uuid__": "adc3de19-fa1a-4de7-bc2e-b4d8afabc439"}, "_isSystemFontUsed": false, "_spacingX": 0, "_batchAsBitmap": false, "_N$horizontalAlign": 0, "_N$verticalAlign": 1, "_N$fontFamily": "<PERSON><PERSON>", "_N$overflow": 0, "_N$cacheMode": 0, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "6bhXGfBy1CLJ/spz05AnKQ", "sync": false}, {"__type__": "cc.Node", "_name": "backgroundSlot", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 38}], "_active": true, "_level": 3, "_components": [], "_prefab": {"__id__": 86}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 954, "height": 512}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.3}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -95, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "cc.Node", "_name": "spinView", "_objFlags": 0, "_parent": {"__id__": 37}, "_children": [{"__id__": 39}, {"__id__": 42}, {"__id__": 45}, {"__id__": 48}, {"__id__": 51}, {"__id__": 54}, {"__id__": 57}, {"__id__": 60}, {"__id__": 63}, {"__id__": 66}, {"__id__": 69}, {"__id__": 72}, {"__id__": 75}, {"__id__": 78}, {"__id__": 81}], "_active": true, "_level": 4, "_components": [{"__id__": 84}], "_prefab": {"__id__": 85}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 900, "height": 494}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 55, 0, 0, 0, 0, 1, 0.7, 0.7, 1]}}, {"__type__": "cc.Node", "_name": "skeleton1", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 40}], "_prefab": {"__id__": 41}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-336, 171, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 39}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "65nO36LuJOfZrkhLuz13va", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton2", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 43}], "_prefab": {"__id__": 44}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, 171, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 42}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "23EMd7fCtCLp9VVZ0ghPD6", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton3", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 46}], "_prefab": {"__id__": 47}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 171, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 45}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "3eo359hURA8os7PztIxa4k", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton4", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 49}], "_prefab": {"__id__": 50}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [168, 171, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 48}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "aen5WEGVtMhpyAp71WOhHz", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton5", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 52}], "_prefab": {"__id__": 53}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [336, 171, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 51}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "a3QksOrPNBKKVCFTS2oh5e", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton6", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 55}], "_prefab": {"__id__": 56}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-336, 6, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 54}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "4fHii+SClP1KDI/ppVovJ/", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton7", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 58}], "_prefab": {"__id__": 59}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, 6, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 57}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "b9gk1p2yNIEZ6PbAvAGWE5", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton8", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 61}], "_prefab": {"__id__": 62}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 6, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 60}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "c0V6EqMIZCraHuxcwc7l/t", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton9", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 64}], "_prefab": {"__id__": 65}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [168, 6, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 63}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "0e0e0pwN1OeIh8UOesl1JK", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton10", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 67}], "_prefab": {"__id__": 68}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [336, 6, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 66}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "f3GRc4MnZDFLYoNkvkZ4AZ", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton11", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 70}], "_prefab": {"__id__": 71}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-336, -159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 69}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "c2fN7fBaJMwJLMFExy3tMk", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton12", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 73}], "_prefab": {"__id__": 74}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-168, -159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 72}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "cdAnqqSRdGfahUTQhW0mQC", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton13", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 76}], "_prefab": {"__id__": 77}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 75}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "661ctlUABFOYlzD4f27anl", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton14", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 79}], "_prefab": {"__id__": 80}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [168, -159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 78}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "2blAH7zsVImJFoPoFHClkD", "sync": false}, {"__type__": "cc.Node", "_name": "skeleton15", "_objFlags": 0, "_parent": {"__id__": 38}, "_children": [], "_active": true, "_level": 5, "_components": [{"__id__": 82}], "_prefab": {"__id__": 83}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 163, "height": 191.08}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_quat": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_skewX": 0, "_skewY": 0, "groupIndex": 0, "_id": "", "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [336, -159, 0, 0, 0, 0, 1, 1, 1, 1]}}, {"__type__": "sp.Skeleton", "_name": "", "_objFlags": 0, "node": {"__id__": 81}, "_enabled": true, "paused": false, "defaultSkin": "", "defaultAnimation": "", "_preCacheMode": 0, "_cacheMode": 0, "loop": true, "premultipliedAlpha": false, "timeScale": 1, "_accTime": 0, "_playCount": 0, "_frameCache": null, "_curFrame": null, "_skeletonCache": null, "_animationName": "", "_animationQueue": [], "_headAniInfo": null, "_playTimes": 0, "_isAniComplete": true, "_N$skeletonData": {"__uuid__": "3e81cf48-2998-46e8-b84f-2b65bc0998f7"}, "_N$_defaultCacheMode": 0, "_N$debugSlots": false, "_N$debugBones": false, "_N$useTint": false, "_N$enableBatch": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "bbsv4gZypOMrdPKAMFvq1E", "sync": false}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "node": {"__id__": 38}, "_enabled": true, "_spriteFrame": null, "_type": 0, "_segments": 64, "_N$alphaThreshold": 1, "_N$inverted": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "ed/tvt+qFDZ4fkHZLImp5W", "sync": false}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "5aJ+lT/QRD6oAIzg/Ew0gl", "sync": false}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_defaultClip": {"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, "_clips": [{"__uuid__": "fd368a1d-24a5-4b8f-8745-d532e9a6fda0"}, {"__uuid__": "2c581fef-58f4-478d-adee-4138a71c7df4"}], "playOnLoad": false, "_id": ""}, {"__type__": "bfff4nSfydEvoywHot7bNxR", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "lbSessionID": {"__id__": 23}, "lbTotalBet": {"__id__": 29}, "lbTotalWin": {"__id__": 35}, "skeletonIcons": [{"__id__": 40}, {"__id__": 43}, {"__id__": 46}, {"__id__": 49}, {"__id__": 52}, {"__id__": 55}, {"__id__": 58}, {"__id__": 61}, {"__id__": 64}, {"__id__": 67}, {"__id__": 70}, {"__id__": 73}, {"__id__": 76}, {"__id__": 79}, {"__id__": 82}], "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "*************-4267-8e2f-9cc56b10b1aa"}, "fileId": "041usxavRJ/bZjULEhQSem", "sync": false}]