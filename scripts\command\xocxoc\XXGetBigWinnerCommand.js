/*
 * Generated by BeChicken
 * on 10/30/2019
 * version v1.0
 */
(function () {
    var XXGetBigWinnerCommand;

    XXGetBigWinnerCommand = (function () {
        function XXGetBigWinnerCommand() {
        }

        XXGetBigWinnerCommand.prototype.execute = function (controller) {
            var url = 'api/XocDia/GetBigWinner';

            return cc.ServerConnector.getInstance().sendRequest(cc.SubdomainName.XOC_XOC, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onXXGetBigWinnerResponse(obj);
            });
        };

        return XXGetBigWinnerCommand;

    })();

    cc.XXGetBigWinnerCommand = XXGetBigWinnerCommand;

}).call(this);