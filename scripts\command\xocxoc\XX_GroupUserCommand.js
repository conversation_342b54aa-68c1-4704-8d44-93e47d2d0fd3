/*
 * Generated by BeChicken
 * on 10/22/2019
 * version v1.0
 */
(function () {
    var XXGroupUserCommand;

    XXGroupUserCommand = (function () {
        function XXGroupUserCommand() {
        }

        XXGroupUserCommand.prototype.execute = function (controller) {
            let url = 'api/XocDia/GetPlayersNotInGame';
            let subDomainName = cc.SubdomainName.XOC_XOC;
            return cc.ServerConnector.getInstance().sendRequest(subDomainName, url, function (response) {
                var obj = JSON.parse(response);
                return controller.onGetGroupUserResponse(obj);
            });
        };

        return XXGroupUserCommand;

    })();

    cc.XXGroupUserCommand = XXGroupUserCommand;

}).call(this);