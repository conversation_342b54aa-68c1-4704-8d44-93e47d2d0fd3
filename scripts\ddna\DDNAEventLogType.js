/**
 * Created by <PERSON><PERSON><PERSON> on 3/20/2019.
 */

(function () {
    cc.DDNAEventLogType = cc.Enum({
        //server
        TREASURE_GET_CARROT_CARD: 'TREASURE_GET_CARROT_CARD',
        TREASURE_GET_CARROT_BANK: 'TREASURE_GET_CARROT_BANK',
        TREASURE_GET_CARROT_VP: 'TREASURE_GET_CARROT_VP',
        //local
        TREASURE_GET_CARROT_LOGIN: 'TREASURE_GET_CARROT_LOGIN',
        TREASURE_GET_CARROT_JUMP: 'TREASURE_GET_CARROT_JUMP',
        TREASURE_GET_CARROT_CHEST: 'TREASURE_GET_CARROT_CHEST',
        TREASURE_GET_CARROT_BUY: 'TREASURE_GET_CARROT_BUY',
    });

}).call(this);
