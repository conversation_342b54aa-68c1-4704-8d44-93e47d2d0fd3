/**
 * Created by <PERSON><PERSON><PERSON> on 3/20/2019.
 */

(function () {
    cc.DDNATransactionName = cc.Enum({
        VIETTEL: 'VIETTEL',
        VINA: 'VINA',
        MOBI: 'MOBI',
        AGENCY: 'AGENCY',
        TRADE: 'TRADE',
        GIFTCODE: 'GIFTCODE',
        VIETTEL_BONUS: 'VIETTEL_BONUS',
        VINA_BONUS: 'VINA_BONUS',
        MOBI_BONUS: 'MOBI_BONUS',

        VIP: 'VIP',
        VQMM: 'VQMM',
        VIP_LOAN: 'VIP_LOAN',
        VIP_LOAN_RETURN: 'VIP_LOAN_RETURN',
        VIP_PRIZE: 'VIP_PRIZE',

        BUY_CARROT: 'BUY_CARROT',
    });

}).call(this);
