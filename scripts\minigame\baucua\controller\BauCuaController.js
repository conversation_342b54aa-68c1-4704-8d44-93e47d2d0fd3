/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    var BauCuaController;
    BauCuaController = (function () {
        var instance;

        function BauCuaController() {

        }

        instance = void 0;

        BauCuaController.getInstance = function () {
            if (instance === void 0) {
                instance = this;
            }
            return instance.prototype;
        };

        // Cap nhat game theo HubOn
        //BauCuaView
        BauCuaController.prototype.setBauCuaView = function (view) {
            return this.BauCuaView = view;
        };
        BauCuaController.prototype.sendRequestOnHub = function (method, data1, data2) {
            if (this.BauCuaView)
                return this.BauCuaView.sendRequestOnHub(method, data1, data2);
        };
        //Cap nhat so du cua nguoi choi
        BauCuaController.prototype.updateBalance = function (balance) {
            this.BauCuaView.updateBalance(balance)
        };
        //HUB ON NAME
        BauCuaController.prototype.setInfoView = function (infoView) {
            return this.BauCua_InfoView = infoView;
        };
        //playerLeave
        BauCuaController.prototype.playerLeave = function (info) {
            this.BauCuaView.playerLeave(info);
            this.BauCua_InfoView.playerLeave(info);
        };
        //HubOn updateSessionInfo
        BauCuaController.prototype.updateSessionInfo = function (data) {
            return this.BauCua_InfoView.updateSessionInfo(data);
        };
        //HubOn summaryPlayer
        BauCuaController.prototype.updatePlayersInGame = function (data) {
            return this.BauCua_InfoView.updatePlayersInGame(data);
        };

        //HubOn showResult
        BauCuaController.prototype.winResult = function (result) {
            return this.BauCua_InfoView.winResult(result);
        };
        BauCuaController.prototype.setWinResult = function (result) {
            return this.winResultResponse = result;
        };
        BauCuaController.prototype.getWinResult = function () {
            return this.winResultResponse;
        };
        BauCuaController.prototype.setWinVipResult = function (result) {
            return this.winVipResultResponse = result;
        };
        BauCuaController.prototype.getWinVipResult = function () {
            return this.winVipResultResponse;
        };
        BauCuaController.prototype.setTotalWinResult = function (result) {
            return this.totalWinResultResponse = result;
        };
        BauCuaController.prototype.getTotalWinResult = function () {
            return this.totalWinResultResponse;
        };
        //HubOn winResultVip
        BauCuaController.prototype.winResultVip = function (result) {
            return this.BauCua_InfoView.winResultVip(result);
        };
        BauCuaController.prototype.updateTotalUserWin = function (amount) {
            return this.BauCua_InfoView.updateTotalUserWin(amount);
        };


        //Hien thi message chat
        BauCuaController.prototype.playerShowBubbleChat = function (message) {
            return this.BauCua_InfoView.playerShowBubbleChat(message);
        };

        //Cap nhat thong tin player
        BauCuaController.prototype.updatePlayersUI = function (data) {
            return this.BauCua_InfoView.updatePlayersUI(data);
        };
        //Cap nhat thong tin nguoi choi hien tai
        BauCuaController.prototype.updatePlayerInfor = function (data) {
            return this.BauCua_InfoView.updatePlayerInfor(data);
        };

        //Cap nhat balance
        BauCuaController.prototype.updateBalanceCurrPlayer = function (balance) {
            return this.BauCua_InfoView.updateBalanceCurrPlayer(balance);
        };
        //Cap nhat balance cua player khac
        BauCuaController.prototype.updateBalancePlayer = function (data) {
            return this.BauCua_InfoView.updateBalancePlayer(data);
        };
        //Cap nhat thoi gian
        BauCuaController.prototype.updateRoomTimer = function (time) {
            return this.BauCua_InfoView.updateRoomTimer(time);
        };

        //HubOn onNotifyChangePhrase
        BauCuaController.prototype.onNotifyChangePhrase = function (data) {
            return this.BauCua_InfoView.onNotifyChangePhrase(data);
        };
        //Cap nhat position Player
        BauCuaController.prototype.updatePositionPlayerUI = function (positions) {
            return this.positionsUI = positions;
        };
        BauCuaController.prototype.positionPlayerUI = function () {
            return this.positionsUI;
        };

        //Assets
        BauCuaController.prototype.setAssetView = function (assetsView) {
            return this.BauCua_AssetsView = assetsView;
        };

        BauCuaController.prototype.getAssetView = function () {
            return this.BauCua_AssetsView;
        };

        BauCuaController.prototype.createChip = function (type) {
            return this.BauCua_AssetsView.createChip(type);
        };

        BauCuaController.prototype.getSfNan = function (isNan) {
            return this.BauCua_AssetsView.getSfNan(isNan);
        };

        BauCuaController.prototype.getSfDice = function (index) {
            return this.BauCua_AssetsView.getSfDice(index);
        };
        //Lay mau timer
        BauCuaController.prototype.getColorType = function (type) {
            return this.BauCua_AssetsView.getColorType(type);
        };
        //Luu chip vao pool
        BauCuaController.prototype.putChipToPool = function (nodeChip, betValue) {
            return this.BauCua_AssetsView.putChipToPool(nodeChip, betValue);
        };
        //Clear pools
        BauCuaController.prototype.clearPools = function () {
            return this.BauCua_AssetsView.clearPools();
        };

        //ResultView
        BauCuaController.prototype.setResultView = function (resultView) {
            this.resultView = resultView;
        };
        BauCuaController.prototype.onShowResult = function (result) {
            return this.resultView.onShowResult(result);
        };
        BauCuaController.prototype.setDicesResult = function (result) {
            return this.resultView.setDicesResult(result);
        };

        //BetView
        BauCuaController.prototype.setBetView = function (betView) {
            return this.BauCua_BetView = betView;
        };
        BauCuaController.prototype.onBet = function (betSide) {
            return this.BauCua_BetView.onBet(betSide);
        };

        //Enable cac cua bet
        BauCuaController.prototype.enableClickBet = function (enable) {
            return this.BauCua_BetView.enableClickBet(enable);
        };

        //Disable button bet lai
        BauCuaController.prototype.disableBetAgain = function (isDisable) {
            return this.BauCua_BetView.disableBetAgain(isDisable);
        };
        //Hien thi hieu ung thang cua cua bet
        BauCuaController.prototype.playAnimationWin = function (sideWin) {
            return this.BauCua_BetView.playAnimationWin(sideWin);
        };
        //Stop hien thi hieu ung thang
        BauCuaController.prototype.stopAnimationWin = function () {
            return this.BauCua_BetView.stopAnimationWin();
        };
        //Cap nhat tong tien bet player
        BauCuaController.prototype.updateTotalUserBetSide = function (betSide, total) {
            return this.BauCua_BetView.updateTotalUserBetSide(betSide, total);
        };
        BauCuaController.prototype.updateTotalBet = function (data) {
            return this.BauCua_BetView.updateTotalBet(data);
        };
        //UpdateBetOfAccount
        BauCuaController.prototype.updateBetOfAccount = function (data) {
            return this.BauCua_BetView.updateBetOfAccount(data);
        };

        //ChipView
        BauCuaController.prototype.setChipsView = function (chipsView) {
            return this.BauCua_ChipsView = chipsView;
        };
        //Di chuyen chip
        BauCuaController.prototype.moveChipBet = function (betValue, betSide, type, accID) {
            return this.BauCua_ChipsView.moveChipBet(betValue, betSide, type, accID);
        };

        //Lay lai chip thua
        BauCuaController.prototype.getChipsLose = function (sideLose, isTie) {
            return this.BauCua_ChipsView.getChipsLose(sideLose, isTie);
        };
        //Tra chip thang
        BauCuaController.prototype.refundChips = function (sideWin) {
            return this.BauCua_ChipsView.refundChips(sideWin);
        };

        //ClearAllChip
        BauCuaController.prototype.clearAllChips = function () {
            return this.BauCua_ChipsView.clearAllChips();
        };

        //Khoi tao lai param chip
        BauCuaController.prototype.initParamChips = function () {
            return this.BauCua_ChipsView.initParamChips();
        };

        //Update chip khi vao game
        BauCuaController.prototype.updateChipForBetSession = function (data) {
            return this.BauCua_ChipsView.updateChipForBetSession(data);
        };

        BauCuaController.prototype.clearBetLog = function (sessionID) {
            this.betLog = this.betLog.filter(log => log.sessionID > (sessionID - 1));
        };
        BauCuaController.prototype.getBetLogBySessionID = function (sessionID) {
            return this.betLog.filter(log => log.sessionID == sessionID - 1);
        };
        //Set betBlog
        BauCuaController.prototype.setBetLog = function (betInfo) {
            return this.betLog.push(betInfo);
        };
        //Lay thong tin betLog
        BauCuaController.prototype.getBetLog = function () {
            return this.betLog;
        };
        //Khoi tao/ reset betLog
        BauCuaController.prototype.initBetLog = function () {
            return this.betLog = [];
        };

        //Set betBlog
        BauCuaController.prototype.setChipWin = function (chip, chipType, positionEnd) {
            return this.chipsWin.push([chip, chipType, positionEnd]);
        };
        //Lay thong tin chipWin
        BauCuaController.prototype.getChipsWin = function () {
            return this.chipsWin;
        };
        //Khoi tao/ reset chipWin
        BauCuaController.prototype.initChipsWin = function () {
            return this.chipsWin = [];
        };

        //Set betBlogSession
        BauCuaController.prototype.setBetLogSession = function (sessionId) {
            return this.betLogSession = sessionId;
        };
        //Lay thong tin betLogSession
        BauCuaController.prototype.getBetLogSession = function () {
            return this.betLogSession;
        };

        //Set CurrentState
        BauCuaController.prototype.setCurrentState = function (state) {
            return this.currentState = state;
        };
        //Get CurrentState
        BauCuaController.prototype.getCurrentState = function () {
            return this.currentState;
        };

        //Set Nan
        BauCuaController.prototype.setIsNan = function (isNan) {
            return this.isNan = isNan;
        };
        //Get Nan
        BauCuaController.prototype.getIsNan = function () {
            return this.isNan;
        };
        return BauCuaController;


    })();
    cc.BauCuaController = BauCuaController;
}).call(this);