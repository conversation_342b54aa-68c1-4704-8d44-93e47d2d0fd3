/*
 * Generated by BeChicken
 * on 11/14/2019
 * version v1.0
 */
(function () {
    cc.BauCuaGraphItem = cc.Class({
        extends: cc.Component,
        properties: {
            spriteDice1: cc.Sprite,
            spriteDice2: cc.Sprite,
            spriteDice3: cc.Sprite,
        },
        setResultDice: function (data) {
            this.spriteDice1.spriteFrame = cc.BauCuaController.getInstance().getSfDice(parseInt(data.FirstDice) - 1);
            this.spriteDice2.spriteFrame = cc.BauCuaController.getInstance().getSfDice(parseInt(data.SecondDice) - 1);
            this.spriteDice3.spriteFrame = cc.BauCuaController.getInstance().getSfDice(parseInt(data.ThirdDice) - 1);
        }
    })
}).call(this);