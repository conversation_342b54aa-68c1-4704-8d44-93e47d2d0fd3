/*
 * Generated by BeChicken
 * on 11/14/2019
 * version v1.0
 */
(function () {
    cc.BauCuaGraphView = cc.Class({
        extends: cc.PopupBase,
        properties: {
            lbTotalDeer: cc.Label,//Huou
            lbTotalGourd: cc.Label,//Bau
            lbTotalRooster: cc.Label,//Ga
            lbTotalFish: cc.Label,//Ca
            lbTotalCrab: cc.Label,//Cua
            lbTotalLobster: cc.Label,//Tom
            itemDice: cc.Prefab,
            layoutDices: cc.Node,
        },

        onLoad: function () {
            this.animation = this.node.getComponent(cc.Animation);
            //Khoi tao pool item dices
            this.poolItemDice = new cc.NodePool();
            for (let i = 0; i < 50; i++) {
                let item = cc.instantiate(this.itemDice);
                this.poolItemDice.put(item);
            }
        },
        resetList: function () {
            try {
                this.lbTotalDeer.string = 0;
                this.lbTotalGourd.string = 0;
                this.lbTotalRooster.string = 0;
                this.lbTotalFish.string = 0;
                this.lbTotalCrab.string = 0;
                this.lbTotalLobster.string = 0;
                this.layoutDices.removeAllChildren(true);
            } catch (e) {

            }
        },
        onEnable: function () {
            this.resetList();

            var self = this;
            var delay = 0.2;
            cc.director.getScheduler().schedule(function () {
                self.getSoiCau();
            }, this, 1, 0, delay, false);

            this.animation.play('openPopup');

            this.totalDeer = 0;//Huou
            this.totalGourd = 0;//Bau
            this.totalRooster = 0;//Ga
            this.totalFish = 0;//Ca
            this.totalCrab = 0;//Cua
            this.totalLobster = 0; //Tom
        },

        getSoiCau: function () {
            var command = new cc.BauCuaSoiCauCommand;
            command.execute(this)
        },
        onResponse: function (list) {
            if (list) {
                //Tinh tong
                list.map((item, index) => {
                    this.calcSumDice(item.FirstDice);
                    this.calcSumDice(item.SecondDice);
                    this.calcSumDice(item.ThirdDice);
                    //map item

                    let itemDiceUI = this.poolItemDice.get();
                    if(index !== 0) {
                        itemDiceUI.opacity = 150;
                    }
                    itemDiceUI.getComponent('BauCuaGraphItem').setResultDice(item);
                    itemDiceUI.parent = this.layoutDices;
                }, this);
                //Hien thi tong tung cua
                this.lbTotalDeer.string = this.totalDeer;
                this.lbTotalGourd.string = this.totalGourd;
                this.lbTotalRooster.string = this.totalRooster;
                this.lbTotalFish.string = this.totalFish;
                this.lbTotalCrab.string = this.totalCrab;
                this.lbTotalLobster.string = this.totalLobster;
            }
        },
        onDisable: function () {
            this.resetList();
        },
        //Tinh tong xuat hien tung cua bet
        calcSumDice: function (side) {
            side = parseInt(side);
            switch (side) {
                case cc.BauCuaBetSide.Deer://Huou
                    this.totalDeer += 1;
                    break;
                case cc.BauCuaBetSide.Gourd://Bau
                    this.totalGourd += 1;
                    break;
                case cc.BauCuaBetSide.Rooster://Ga
                    this.totalRooster += 1;
                    break;
                case cc.BauCuaBetSide.Fish://Ca
                    this.totalFish += 1;
                    break;
                case cc.BauCuaBetSide.Crab://Cua
                    this.totalCrab += 1;
                    break;
                case cc.BauCuaBetSide.Lobster://Tom
                    this.totalLobster += 1;
                    break;
            }
        },
        closeClicked: function () {
            this.animation.play('closePopup');
            var self = this;
            var delay = 0.12;
            cc.director.getScheduler().schedule(function () {
                self.animation.stop();
                cc.BauCuaPopupController.getInstance().destroyGraphView();
            }, this, 1, 0, delay, false);
        },
    })
}).call(this)