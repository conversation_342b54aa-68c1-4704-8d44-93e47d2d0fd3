/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    cc.BauCuaGroupUserView = cc.Class({
        extends: cc.BacaratGroupUserView,
        properties: {},
        getGroupUser: function () {
            var getGroupUserCommand = new cc.BauCuaGroupUserCommand;
            getGroupUserCommand.execute(this);
        },

        onGetGroupUserResponse: function (response) {
            var list = response;
            //var list = slotsHistoryListData;
            if (list !== null && list.length > 0) {
                this.groupUserListView.resetList();
                this.groupUserListView.initialize(list);
            }
        },

        closeClicked: function () {
            this.groupUserListView.resetList();
            this.animation.play('closePopup');
            var self = this;
            var delay = 0.12;
            cc.director.getScheduler().schedule(function () {
                self.animation.stop();
                cc.BauCuaPopupController.getInstance().destroyGroupUserView();
            }, this, 1, 0, delay, false);
        },
    })
}).call(this)