/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    cc.BauCuaBetView = cc.Class({
        extends: cc.Component,
        properties: {
            nodeDeer: cc.Node,//Btn Huou
            nodeGourd: cc.Node,//Btn Bau
            nodeRooster: cc.Node,//Btn Ga
            nodeFish: cc.Node,//Btn Ca
            nodeCrab: cc.Node,//Btn Cua
            nodeLobster: cc.Node,//Btn Tom

            //lbTotal bet huou
            lbTotalUserBetDeer: cc.Label,
            lbTotalDeer: cc.Label,
            //lbTotal bet bau
            lbTotalUserBetGourd: cc.Label,
            lbTotalGourd: cc.Label,
            //lbTotal bet ga
            lbTotalUserBetRooster: cc.Label,
            lbTotalRooster: cc.Label,
            //lbTotal bet ca
            lbTotalUserBetFish: cc.Label,
            lbTotalFish: cc.Label,
            //lbTotal bet cua
            lbTotalUserBetCrab: cc.Label,
            lbTotalCrab: cc.Label,
            //lbTotal bet tom
            lbTotalUserBetLobster: cc.Label,
            lbTotalLobster: cc.Label,

            //ButtonBet
            btn1K: cc.Node,
            btn5K: cc.Node,
            btn10K: cc.Node,
            btn100K: cc.Node,
            btn500K: cc.Node,
            btn5M: cc.Node,

            //Btn Bet again
            nodeBetX2: cc.Node,
            nodeBetAgain: cc.Node,

            spriteBtnNan: cc.Sprite

        },
        onLoad: function () {
            this.controller = cc.BauCuaController.getInstance();
            this.controller.setBetView(this);

            this.betValue = 5000;//Khoi tao bet value 5k
            this.listNodeSide = [this.nodeDeer, this.nodeGourd, this.nodeRooster,
                this.nodeFish, this.nodeCrab, this.nodeLobster];
            //An animWin
            this.stopAnimationWin();
            //Btn Bet
            this.btnBetX2 = this.nodeBetX2.getComponent(cc.Button);
            this.btnBetAgain = this.nodeBetAgain.getComponent(cc.Button);
            this.controller.setIsNan(false);

            this.lstBtnBet = this.getListBtnBet([
                this.nodeDeer,
                this.nodeGourd,
                this.nodeRooster,
                this.nodeFish,
                this.nodeCrab,
                this.nodeLobster
            ]);
            this.enableClickBet(false);

            this.setSpriteBtnNan();
        },
        getListBtnBet: function (arrNode) {
            let listBtn = [];
            arrNode.map(node => listBtn.push(node.getChildByName('btnBet').getComponent(cc.Button)));
            return listBtn;
        },

        //Hien thi side thang
        activeNodeWin: function (node, isActive) {
            let nodeWin = node.getChildByName('win');
            nodeWin.active = isActive;
        },
        //Reset label bet
        resetLbTotalBet: function () {
            let lstLabel = [
                this.lbTotalUserBetDeer, this.lbTotalDeer,
                this.lbTotalUserBetGourd, this.lbTotalGourd,
                this.lbTotalUserBetRooster, this.lbTotalRooster,
                this.lbTotalUserBetFish, this.lbTotalFish,
                this.lbTotalUserBetCrab, this.lbTotalCrab,
                this.lbTotalUserBetLobster, this.lbTotalLobster
            ];
            lstLabel.map(label => label.string = "");
        },
        enableClickBet: function (isEnable) {
            this.disableBetAgain(!isEnable);
            this.lstBtnBet.map(btn => {
                btn.interactable = isEnable;
            });
        },
        onEnable: function () {
        },
        onDisable: function () {
        },
        //Set gia tri bet
        setBetValue: function (sender, value) {
            value = parseInt(value);
            if (value != this.betValue) {
                this.betValue = value;
                cc.AudioController.getInstance().playSound(cc.AudioTypes.CHIP_SELECT);
                //Doi active chip
                this.resetSpriteButton();
                //Active button
                this.setActiveButton(sender.target);
            }
        },
        setActiveButton: function (node) {
            node.getChildByName('active').active = true;
            let moveUp = cc.moveTo(0.3, cc.v2(node.x, -297));
            node.runAction(moveUp);
        },
        resetSpriteButton: function () {
            let listButton = [this.btn1K, this.btn5K, this.btn10K, this.btn100K, this.btn500K, this.btn5M];
            let self = this;
            listButton.forEach(function (btn, index) {
                self.setDefaultSfButton(btn, index);
            });

        },
        setDefaultSfButton: function (node, type) {
            node.getChildByName('active').active = false;
            let moveBack = cc.moveTo(0.3, cc.v2(node.x, -312));
            node.runAction(moveBack);
        },
        //Bet lai
        onBetAgain: function (sender, unit) {
            unit = parseInt(unit); //He so bet
            let logBet = this.controller.getBetLogBySessionID(this.controller.getBetLogSession());
            if (logBet.length === 0) {
                cc.PopupController.getInstance().showSlotsMessage("Chưa có dữ liệu của phiên trước.");
                return;
            }

            let currLog = this.getLogBetInfo(unit);


            //for (let i = 1; i <= unit; i++) {
            currLog.map((betData, index) => {
                let timeOut = setTimeout(function () {
                    if (this.controller.getCurrentState() === cc.BauCuaPharse.Betting && betData.sessionID === this.controller.getBetLogSession() - 1) {
                        this.sendRequestBet(betData.value, betData.betSide);
                    } else {
                        try {
                            clearTimeout(timeOut);
                        } catch (e) {
                            console.log(e);
                        }
                    }
                }.bind(this), index * 100);
            }, this);
            //}
            this.disableBetAgain(true);
        },

        //Lay thong tin lich su bet
        getLogBetInfo: function (unit) {

            let logBet = this.controller.getBetLogBySessionID(this.controller.getBetLogSession());
            // Id cua bet: Bau, Cua, Ca, Ga, Tom, Huou
            let listSide = [1, 2, 3, 4, 5, 6];
            let dataLogSide = [];

            //Map thong tin log bet cac cua bet
            if (logBet.length > 0) {
                listSide.map(side => {
                    dataLogSide[side] = logBet.filter(betData => betData.betSide == side);
                }, this);
            }

            //Tinh toan chip tung cua bet
            let finalLog = [];

            //Test Total
            dataLogSide.map((logSide) => {
                if (logSide.length > 0) {
                    let bet = this.calcLogBet(logSide, unit);
                    finalLog.push(...bet);
                }
            }, this);

            return finalLog;
        },
        //Tinh tong tien bet cua tung cua
        getTotalMoneyBetSide: function (logsBetSide, unit) {
            let totalMoney = 0;
            logsBetSide.map(betData => {
                totalMoney += parseInt(betData.value);
            }, this);
            return totalMoney * unit;
        },
        //Tinh toan luot bet
        calcLogBet: function (logsBetSide, unit) {

            let listChip = [5000000, 500000, 100000, 10000, 5000, 1000];
            let totalMoney = this.getTotalMoneyBetSide(logsBetSide, unit);
            let listCalc = [];

            // Duyet tung chip trong danh sach chip
            for (let i in listChip) {
                let chip = listChip[i];
                // So luong chip tuong ung voi tong tien hien tai
                let countBets = Math.floor(totalMoney / chip);
                // Tinh toan so luong chip con lai tuong ung voi so tien con lai
                totalMoney -= countBets * chip;
                // gan chip vao list
                listCalc.push([chip, countBets]);
            }
            // Lay chip co so luong > 0
            let listLogBet = listCalc.filter(chip => chip[1] !== 0);

            //Chuyen data sang thong tin bet
            let logsBet = [];
            listLogBet.map(chipItem => {
                let countBets = chipItem[1];
                let value = chipItem[0];
                for (let i = 0; i < countBets; i++) {
                    logsBet.push({value: value, betSide: logsBetSide[0].betSide, sessionID: logsBetSide[0].sessionID});
                }
            }, this);
            return logsBet;
        },
        //Disable button bet lai, bet x2
        disableBetAgain: function (isDisable) {
            this.btnBetAgain.interactable = !isDisable;
            this.btnBetX2.interactable = !isDisable;
            let color = cc.Color.WHITE;
            if (isDisable) {
                color = cc.Color.GRAY;
            }
            this.nodeBetX2.color = color;
            this.nodeBetAgain.color = color;
        },
        //Chay animation win
        playAnimationWin: function (sideWin) {
            sideWin = parseInt(sideWin);
            let nodeWin = null;
            switch (sideWin) {
                case cc.BauCuaBetSide.Deer:
                    nodeWin = this.nodeDeer;
                    break;
                case cc.BauCuaBetSide.Gourd:
                    nodeWin = this.nodeGourd;
                    break;
                case cc.BauCuaBetSide.Rooster:
                    nodeWin = this.nodeRooster;
                    break;
                case cc.BauCuaBetSide.Fish:
                    nodeWin = this.nodeFish;
                    break;
                case cc.BauCuaBetSide.Crab:
                    nodeWin = this.nodeCrab;
                    break;
                case cc.BauCuaBetSide.Lobster:
                    nodeWin = this.nodeLobster;
                    break;
            }
            if (nodeWin != null) {
                this.activeNodeWin(nodeWin, true);
            }

        },
        //Stop play win
        stopAnimationWin: function () {
            this.listNodeSide.map(node => {
                this.activeNodeWin(node, false);
            }, this);
            this.resetLbTotalBet();
        },
        onBetClick: function (sender, betSide) {
            this.onBet(betSide);
        },
        //onBet dat cua
        onBet: function (betSide) {
            this.disableBetAgain(true);
            cc.AudioController.getInstance().playSound(cc.AudioTypes.CHIP_BET);
            //Gui request bet
            this.sendRequestBet(this.betValue, betSide);
        },
        //Update betValue
        updateTotalUserBetSide: function (betSide, totalBet) {
            betSide = parseInt(betSide);
            let label = this.getLabelTotalBetBySide(betSide);
            label.string = cc.Tool.getInstance().formatNumber(totalBet);
        },
        //HubOn betOfAccount
        updateBetOfAccount: function (data) {
            data.map(betSide => {
                let side = parseInt(betSide.BetSide);
                let label = this.getLabelTotalBetBySide(side);
                label.string = cc.Tool.getInstance().formatNumber(betSide.BetValue);
            });
        },
        getLabelTotalBetBySide: function (betSide) {
            let label = null;
            switch (betSide) {
                case cc.BauCuaBetSide.Deer:
                    label = this.lbTotalDeer;
                    break;
                case cc.BauCuaBetSide.Gourd:
                    label = this.lbTotalGourd;
                    break;
                case cc.BauCuaBetSide.Rooster:
                    label = this.lbTotalRooster;
                    break;
                case cc.BauCuaBetSide.Fish:
                    label = this.lbTotalFish;
                    break;
                case cc.BauCuaBetSide.Crab:
                    label = this.lbTotalCrab;
                    break;
                case cc.BauCuaBetSide.Lobster:
                    label = this.lbTotalLobster;
                    break;
            }
            return label;
        },

        updateTotalBet: function (data) {
            this.setStringValue(this.lbTotalUserBetDeer, data.TotalBetDeer);
            this.setStringValue(this.lbTotalUserBetGourd, data.TotalBetGourd);
            this.setStringValue(this.lbTotalUserBetRooster, data.TotalBetChicken);
            this.setStringValue(this.lbTotalUserBetFish, data.TotalBetFish);
            this.setStringValue(this.lbTotalUserBetCrab, data.TotalBetCrab);
            this.setStringValue(this.lbTotalUserBetLobster, data.TotalBetShrimp);
        },
        setStringValue: function (label, strNum) {
            label.string = strNum === 0 ? "" : cc.Tool.getInstance().formatNumber(strNum);
        },
        sendRequestBet: function (betValue, betSide) {
            return this.controller.sendRequestOnHub(cc.MethodHubName.BET, betValue, betSide);
        },
        setSpriteBtnNan: function () {
            this.spriteBtnNan.spriteFrame = this.controller.getSfNan(this.getIsNan());
        },
        onNanClick: function () {
            this.controller.setIsNan(!this.getIsNan());
            this.setSpriteBtnNan();
        },
        getIsNan: function () {
            return this.controller.getIsNan();

        }

    });
}).call(this);
