/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
(function () {
    cc.BauCuaPopupView = cc.Class({
        "extends": cc.BacaratPopupView,
        properties: {
            prefabGraph: cc.Prefab
        },
        onLoad: function () {
            cc.BauCuaPopupController.getInstance().setPopupView(this);
        },
        createGraphView: function () {
            this.nodeGraph = this.createView(this.prefabGraph);
        },
        destroyGraphView: function () {
            if (this.nodeGraph)
                this.nodeGraph.destroy();
        },
    });
}).call(this);