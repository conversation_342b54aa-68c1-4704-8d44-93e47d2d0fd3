/*
 * Generated by BeChicken
 * on 11/14/2019
 * version v1.0
 */
(function () {
    cc.BauCuaResultView = cc.Class({
        extends: cc.Component,
        properties: {
            spriteFistDice: cc.Sprite,
            spriteSecondDice: cc.Sprite,
            spriteThirdDice: cc.Sprite,
            nodeBatNan: cc.Node,
            nodeBat: cc.Node,
            skeletonBat: sp.Skeleton
        },
        onLoad: function () {
            this.controller = cc.BauCuaController.getInstance();
            this.controller.setResultView(this);
            this.listBetSide = [1, 2, 3, 4, 5, 6];
        },
        setDicesResult: function(data) {
            let result = data.Result;
            let resDice1 = parseInt(result.Dice1);
            let resDice2 = parseInt(result.Dice2);
            let resDice3 = parseInt(result.Dice3);
            this.setDiceResult(this.spriteFistDice, resDice1 - 1);
            this.setDiceResult(this.spriteSecondDice, resDice2 - 1);
            this.setDiceResult(this.spriteThirdDice, resDice3 - 1);
        },
        onShowResult: function (data) {
            this.setDicesResult(data);

            let result = data.Result;
            let resDice1 = parseInt(result.Dice1);
            let resDice2 = parseInt(result.Dice2);
            let resDice3 = parseInt(result.Dice3);

            let arrResult = [resDice1, resDice2, resDice3];
            arrResult = cc.Tool.getInstance().arrUnique(arrResult);

            //Hien thi cua thang
            arrResult.map(side => this.controller.playAnimationWin(side));

            let listWin = arrResult;
            let listLose = [];
            this.listBetSide.map(side => {
                if (!listWin.includes(side)) {
                    listLose.push(side);
                }
            });
            if (!cc.game.isPaused()) {
                //Thu chip cua thua
                listLose.map(gateLose => {
                    this.controller.getChipsLose(gateLose);
                });

                cc.director.getScheduler().schedule(function () {
                    //Tra chip
                    listWin.map(gateWin => {
                        this.controller.refundChips(gateWin);
                    }, this);

                }, this, 0, 0, 1, false);

                cc.director.getScheduler().schedule(function () {
                    let result = this.controller.getWinResult();
                    if (result) {
                        this.controller.winResult(result);
                    }

                    let vipResult = this.controller.getWinVipResult();
                    if (vipResult) {
                        this.controller.winResultVip(vipResult);
                    }
                    let totalWinResult = this.controller.getTotalWinResult();
                    if (totalWinResult) {
                        this.controller.updateTotalUserWin(totalWinResult);
                    }
                }, this, 0, 0, 3.5, false);
            }

        },
        setDiceResult: function (sprite, indexDice) {
            sprite.spriteFrame = this.controller.getSfDice(indexDice);
        }
    });
}).call(this);