/*
 * Generated by BeChicken
 * on 11/13/2019
 * version v1.0
 */
var netConfig = require('NetConfig');
(function () {
    cc.BauCuaView = cc.Class({
        extends: cc.Component,
        properties: {
            //chat
            nodeParentChat: cc.Node,
            prefabChat: cc.Prefab,
        },

        onLoad: function () {
            this.controller = cc.BauCuaController.getInstance();
            this.controller.setBauCuaView(this);
            cc.ChatRoomController.getInstance().setHubView(this);
            this.controller.initBetLog();
            this.controller.setBetLogSession(1);

            this.hubName = cc.HubName.BauCuaHub;
            this.subDomainName = cc.SubdomainName.BAUCUA;
            this.interval = null;
            this.isActiveChat = false;
            this.lastTimeReconnect = (new Date()).getTime();
            this.currentState = -1;
            //id send playNow
            this.idPlayNow = 0;

            //check jonGame
            this.currAccId = null;

            var nodeChat = cc.instantiate(this.prefabChat);
            this.nodeParentChat.addChild(nodeChat);

            cc.PopupController.getInstance().showBusy();
            this.connectHub();
        },

        onDestroy: function () {
            //Exit lobby
            this.sendRequestOnHub(cc.MethodHubName.EXIT_LOBBY);

            cc.LobbyJackpotController.getInstance().pauseUpdateJackpot(false);

            if (this.interval !== null) {
                clearInterval(this.interval);
            }
            if (this.BauCuaHub)
                this.BauCuaHub.disconnect();
            this.unscheduleAllCallbacks();
            this.controller.setBauCuaView(null);

            if (cc.sys.isNative) {
                cc.loader.releaseResDir('baucua/prefabs');
                cc.loader.releaseResDir('baucua/images');
            }
            cc.PopupController.getInstance().hideBusy();
        },

        disconnectAndLogout: function () {
            if (this.BauCuaHub) {
                this.BauCuaHub.disconnect();
            }
            this.lastTimeReconnect = (new Date()).getTime();
        },

        connectHub: function () {
            var negotiateCommand = new cc.NegotiateCommand;
            negotiateCommand.execute(this, this.subDomainName);
        },

        reconnect: function () {
            this.lastTimeReconnect = (new Date()).getTime();
            this.BauCuaHub.connect(this, this.hubName, this.connectionToken, true);
        },

        //data1 = amount
        //data2 = gate
        sendRequestOnHub: function (method, data1, data2) {
            switch (method) {
                case cc.MethodHubName.ENTER_LOBBY:
                    this.BauCuaHub.enterLobby();
                    break;
                case cc.MethodHubName.EXIT_LOBBY:
                    this.BauCuaHub.exitLobby();
                    break;
                case cc.MethodHubName.BET:
                    this.BauCuaHub.bet(data1, data2);//betValue, betSide
                    break;
                case cc.MethodHubName.PLAY_NOW:
                    this.BauCuaHub.playNow();
                    break;
                case cc.MethodHubName.SEND_MESSAGE:
                    this.BauCuaHub.sendRoomMessage(data1);
                    break;
            }
        },

        onSlotsNegotiateResponse: function (response) {
            this.connectionToken = response.ConnectionToken;
            this.BauCuaHub = new cc.Hub;
            this.BauCuaHub.connect(this, this.hubName, response.ConnectionToken);
        },

        onHubMessage: function (response) {
            if (response.M !== undefined && response.M.length > 0) {
                let res = response.M;
                res.map(m => {
                    switch (m.M) {
                        //Thoat game
                        case cc.MethodHubOnName.PLAYER_LEAVE:
                            this.playerLeave(m.A);
                            break;
                        //Thong tin game
                        case cc.MethodHubOnName.SESSION_INFO:
                            // console.log("SESSION_INFO", m.A);
                            let info = m.A[0];
                            this.controller.onNotifyChangePhrase(info);
                            //Cap nhat tong tien bet
                            this.controller.updateTotalBet(info);
                            break;
                        //Thong tin game
                        case cc.MethodHubOnName.NOTIFY_CHANGE_PHRASE:
                            // console.log("NOTIFY_CHANGE_PHRASE", m.A);
                            this.controller.onNotifyChangePhrase(m.A[0]);
                            break;
                        case cc.MethodHubOnName.UPDATE_ROOM_TIME:
                            this.controller.updateRoomTimer(parseInt(m.A[0]));
                            break;
                        //Danh sach nguoi choi
                        case cc.MethodHubOnName.SUMMARY_PLAYER:
                            this.controller.updatePlayersInGame(m.A[0]);
                            break;
                        //Thong tin game
                        case cc.MethodHubOnName.JOIN_GAME:
                            // console.log("JOIN_GAME", m.A);
                            this.controller.updatePlayerInfor(m.A[0]);
                            this.controller.onNotifyChangePhrase(m.A[1]);
                            //Cap nhat tong tien bet cua user
                            if (m.A[3].length > 0) {
                                m.A[3].map(betData => {
                                    this.controller.updateTotalUserBetSide(betData.BetSide, betData.SummaryBet);
                                }, this);
                            }
                            //Cap nhat chip cua phien
                            this.controller.updateChipForBetSession(m.A[4]);
                            break;
                        //Cap nhat danh sach player
                        case cc.MethodHubOnName.VIP_PLAYERS:
                            // console.log("VIP_PLAYERS", m.A);
                            let dataPlayer = m.A[0];
                            if (dataPlayer.length > 0) {
                                this.controller.updatePlayersUI(dataPlayer);
                            }

                            break;
                        //Lich su bet
                        case cc.MethodHubOnName.GAME_HISTORY:
                            // console.log("GAME_HISTORY", m.A);
                            break;
                        //Thong tin bet cua player
                        case cc.MethodHubOnName.BET_OF_ACCOUNT:
                            this.controller.updateBetOfAccount(m.A[0]);
                            break;
                        //Bet thanh cong
                        case cc.MethodHubOnName.BET_SUCCESS:
                            //Push betValue vao betLog
                            let sessionID = this.controller.getBetLogSession();

                            this.controller.setBetLog({
                                sessionID: sessionID,
                                value: m.A[0].BetValue,
                                betSide: m.A[0].BetSide
                            });
                            //Cap nhat balance
                            this.controller.updateBalanceCurrPlayer(m.A[1]);
                            cc.BalanceController.getInstance().updateRealBalance(m.A[1]);

                            this.controller.updateTotalUserBetSide(m.A[0].BetSide, m.A[0].SummaryBet);
                            this.controller.moveChipBet(m.A[0].BetValue, m.A[0].BetSide, cc.BacaratChipOf.PLAYER, m.A[0].AccountID);
                            break;
                        //Nguoi choi bet
                        case cc.MethodHubOnName.PLAYER_BET:
                            if (m.A[0] != cc.LoginController.getInstance().getUserId()) {
                                //Update chip player
                                this.controller.updateBalancePlayer(m.A);
                                //Move Chip
                                this.controller.moveChipBet(m.A[1], m.A[2], cc.BacaratChipOf.USERS, m.A[0]);
                            }
                            break;

                        //Ket qua
                        case cc.MethodHubOnName.WIN_RESULT:
                            // console.log("WIN_RESULT", m.A);
                            //KO hien thi luon khi co winResult
                            this.controller.setWinResult(m.A[0]);
                            cc.BalanceController.getInstance().updateRealBalance(m.A[0].Balance);
                            break;
                        case cc.MethodHubOnName.WIN_RESULT_VIP:
                            // console.log("WIN_RESULT_VIP", m.A);
                            if (m.A.length > 0) {
                                this.controller.setWinVipResult(m.A[0]);
                            }
                            break;
                        case cc.MethodHubOnName.TOTAL_WIN_MONEY:
                            this.controller.setTotalWinResult(parseInt(m.A[0]));
                            break;
                        //thong bao khi dat cuoc
                        case cc.MethodHubOnName.PLAYER_MESSAGE:
                            cc.PopupController.getInstance().showMessage(m.A[0]);
                            break;
                        //thong bao
                        case cc.MethodHubOnName.MESSAGE:
                            if (!cc.game.isPaused())
                                cc.PopupController.getInstance().showMessage(m.A[0]);
                            break;
                        //nhan message chat
                        case cc.MethodHubOnName.RECEIVE_MESSAGE:
                            cc.ChatRoomController.getInstance().addChatContent(m.A);
                            this.controller.playerShowBubbleChat(m.A);
                            break;

                    }
                });

            } else if (response.R && response.R.AccountID) {
                this.currAccId = response.R.AccountID;
                this.sendRequestOnHub(cc.MethodHubName.PLAY_NOW);
                //sau khi enterLobby
                cc.PopupController.getInstance().hideBusy();

            } else {
                //PING PONG
                if (response.I) {
                    this.BauCuaHub.pingPongResponse(response.I);
                }
            }
        },

        onHubOpen: function () {
            cc.PopupController.getInstance().hideBusy();
            this.sendRequestOnHub(cc.MethodHubName.ENTER_LOBBY);
            cc.PopupController.getInstance().showBusy();
        },

        onHubClose: function () {
            //reconnect
            if ((new Date()).getTime() - this.lastTimeReconnect >= netConfig.RECONNECT_TIME * 1000) {
                this.reconnect();
            } else {
                cc.director.getScheduler().schedule(this.reconnect, this, netConfig.RECONNECT_TIME, 0, 0, false);
            }
        },

        onHubError: function () {

        },

        backClicked: function () {
            cc.LobbyController.getInstance().destroyDynamicView(null);
			 cc.LobbyController.getInstance().offuserguest(true);
        },

        //huong dan
        helpClicked: function () {
            cc.BCPopupController.getInstance().createHelpView();
        },

        playerLeave: function (info) {
            var accID = info[0];
            if (accID === cc.LoginController.getInstance().getUserId()) {
                var message = info[1];
                cc.LobbyController.getInstance().destroyDynamicView(null);
                cc.PopupController.getInstance().showMessage(message)
            }
        },

        chatClicked: function () {
            cc.ChatRoomController.getInstance().showChat();
        },

    });

}).call(this);