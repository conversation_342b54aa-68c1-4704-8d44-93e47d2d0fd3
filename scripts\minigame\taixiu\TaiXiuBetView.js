/**
 * Dat cuoc
 */

(function () {
    cc.TaiXiuBetView = cc.Class({
        "extends": cc.Component,
        properties: {
            lbBetTai: cc.Label,
            lbBetXiu: cc.Label,
			nodebettai: cc.Node,
			nodebetxiu: cc.Node,
        },

        onLoad: function () {
            cc.TaiXiuController.getInstance().setTaiXiuBetView(this);
            //this.reset();
        },

        onDestroy: function () {
            cc.TaiXiuController.getInstance().setTaiXiuBetView(null);
        },

        reset: function () {
            this.lbBetTai.string = '0';
            this.lbBetXiu.string = '0';
			this.nodebettai.active = false;
			this.nodebetxiu.active = false;
        },

        updateBetInfo: function (betInfo) {
            this.betSide = betInfo.BetSide;

            if (betInfo.BetSide === cc.TaiXiuBetSide.TAI) {
				this.nodebettai.active = true;
                this.lbBetTai.string = cc.Tool.getInstance().formatNumber(betInfo.BetValue);
            } else {
				this.nodebetxiu.active = true;
                this.lbBetXiu.string = cc.Tool.getInstance().formatNumber(betInfo.BetValue);
            }

        },

        getBetSide: function () {
            return this.betSide;
        },
    });
}).call(this);
