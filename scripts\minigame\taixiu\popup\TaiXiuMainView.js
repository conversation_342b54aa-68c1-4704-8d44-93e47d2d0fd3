/**
 * Created by Nofear on 6/7/2017.
 */

(function () {
    cc.TaiXiuMainView = cc.Class({
        "extends": cc.PopupViewBase,
        properties: {
            prefabGraph: cc.Prefab, //soi cau
        },

        onLoad: function () {
			
            cc.TaiXiuMainController.getInstance().setTaiXiuMainView(this);
        },

        createGraphView: function () {
            this.nodeGraphView = this.createView(this.prefabGraph);
        },

        destroyGraphView: function () {
            if (this.nodeGraphView)
                this.nodeGraphView.destroy();
        },
    });
}).call(this);
